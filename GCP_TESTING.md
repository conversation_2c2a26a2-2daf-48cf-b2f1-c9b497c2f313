# GCP Local Testing Guide

This guide explains how to test your Validator Token Gateway locally with GCP emulators before deploying to the cloud.

## 🎯 Overview

Unlike AWS which has LocalStack, GCP doesn't have a single comprehensive local testing solution. However, Google provides official emulators for key services, and we've created a comprehensive local testing environment that includes:

- **Pub/Sub Emulator** (Official Google emulator)
- **Secret Manager Mock** (Redis-based simulation)
- **Cloud SQL Mock** (PostgreSQL container)
- **Memorystore Mock** (Redis container)

## 🚀 Quick Start

### 1. Setup Local GCP Environment
```bash
# One command setup
make setup-local-gcp
```

This will:
- Start all GCP emulators
- Create required topics and subscriptions
- Setup mock secrets
- Verify all services are running

### 2. Verify Setup
```bash
# Check status of all emulators
make gcp-status

# View emulator logs
make logs-gcp
```

### 3. Run Tests
```bash
# Run GCP-specific tests
make test-local-gcp

# Run all tests with GCP emulators
make test-cov
```

## 🛠️ Available Services

### Pub/Sub Emulator
- **Port**: 8085
- **Project ID**: local-project
- **Topics**:
  - `validator-token-gateway-messages`
  - `validator-notifications`

```bash
# Test Pub/Sub directly
export PUBSUB_EMULATOR_HOST=localhost:8085
gcloud pubsub topics list --project=local-project
```

### Secret Manager Mock
- **Port**: 6380 (Redis)
- **Predefined Secrets**:
  - `projects/local-project/secrets/jwt-secret/versions/latest`
  - `projects/local-project/secrets/db-password/versions/latest`
  - `projects/local-project/secrets/redis-password/versions/latest`

```bash
# Test Secret Manager mock
redis-cli -h localhost -p 6380 get "projects/local-project/secrets/jwt-secret/versions/latest"
```

### Cloud SQL Mock
- **Port**: 5433
- **Database**: validator_gateway
- **User**: gateway_user

```bash
# Connect to Cloud SQL mock
psql -h localhost -p 5433 -U gateway_user -d validator_gateway
```

### Memorystore Mock
- **Port**: 6381
- **Password**: memorystore-password

```bash
# Test Memorystore mock
redis-cli -h localhost -p 6381 -a memorystore-password ping
```

## 🧪 Testing Workflows

### 1. Test Terraform Configuration Locally
```bash
# Start emulators
make setup-local-gcp

# Verify infrastructure matches Terraform
make test-local-gcp

# Check that all expected resources exist
make gcp-status
```

### 2. Test API with GCP Services
```bash
# Start full stack with GCP emulators
make setup-local-gcp

# Test API endpoints that use GCP services
curl http://localhost:8000/auth/pubsub-token \
  -H "Authorization: Bearer <your-jwt-token>"
```

### 3. Integration Testing
```bash
# Run integration tests
uv run pytest tests/integration/ -v

# Run specific GCP tests
uv run pytest tests/integration/test_gcp_emulators.py -v
```

## 📁 File Structure

```
validator-token-gateway/
├── docker-compose.yml              # Base services
├── docker-compose.local-gcp.yml    # GCP emulators
├── local-service-account.json      # Mock service account
├── scripts/
│   └── setup-local-gcp.sh         # Setup script
└── tests/
    └── integration/
        └── test_gcp_emulators.py   # GCP integration tests
```

## 🔧 Manual Setup (Alternative)

If you prefer manual setup:

### 1. Start Base Services
```bash
docker-compose up -d db redis
```

### 2. Start Pub/Sub Emulator
```bash
docker run -d --name pubsub-emulator \
  -p 8085:8085 \
  gcr.io/google.com/cloudsdktool/cloud-sdk:latest \
  gcloud beta emulators pubsub start --host-port=0.0.0.0:8085 --project=local-project
```

### 3. Setup Topics
```bash
export PUBSUB_EMULATOR_HOST=localhost:8085
gcloud config set project local-project
gcloud pubsub topics create validator-token-gateway-messages
gcloud pubsub subscriptions create validator-messages-sub --topic=validator-token-gateway-messages
```

### 4. Start Secret Manager Mock
```bash
docker run -d --name secret-manager-mock \
  -p 6380:6379 \
  redis:7-alpine redis-server
```

## 🚨 Troubleshooting

### Pub/Sub Emulator Issues
```bash
# Check if emulator is running
curl http://localhost:8085

# View emulator logs
docker logs pubsub-emulator

# Restart emulator
make stop-gcp-emulators
make start-gcp-emulators
```

### Secret Manager Mock Issues
```bash
# Test Redis connection
redis-cli -h localhost -p 6380 ping

# Check stored secrets
redis-cli -h localhost -p 6380 keys "*"
```

### Port Conflicts
If you have port conflicts, update the ports in `docker-compose.local-gcp.yml`:
- Pub/Sub: Change `8085:8085` to `8086:8085`
- Secret Manager: Change `6380:6379` to `6381:6379`

## 🔄 Comparison with Real GCP

| Service | Local Emulator | Real GCP | Compatibility |
|---------|---------------|----------|---------------|
| Pub/Sub | Official Emulator | Cloud Pub/Sub | 100% |
| Secret Manager | Redis Mock | Secret Manager | 90% |
| Cloud SQL | PostgreSQL | Cloud SQL | 95% |
| Memorystore | Redis | Memorystore | 100% |

## 📊 Benefits

### ✅ Advantages
- **Fast Development**: No network latency
- **Cost-Free**: No GCP charges during development
- **Offline Testing**: Works without internet
- **Terraform Validation**: Test infrastructure before deployment
- **CI/CD Integration**: Run in automated pipelines

### ⚠️ Limitations
- **Not 100% Feature Parity**: Some advanced features missing
- **No IAM Emulation**: Simplified authentication
- **Performance Differences**: Local vs cloud performance
- **Limited Monitoring**: No Cloud Monitoring integration

## 🎯 Best Practices

1. **Always test locally first** before deploying to GCP
2. **Use integration tests** to verify emulator compatibility
3. **Keep emulator versions updated** for latest features
4. **Document any emulator limitations** your team discovers
5. **Use environment variables** to switch between local and cloud

## 🔗 Useful Commands

```bash
# Complete workflow
make setup-local-gcp     # Setup everything
make test-local-gcp      # Run tests
make gcp-status          # Check status
make clean-gcp           # Clean up

# Individual services
make start-gcp-emulators # Start emulators only
make stop-gcp-emulators  # Stop emulators
make logs-gcp            # View logs

# Development
make dev                 # Start API with emulators
make docker-up           # Full stack + emulators
```

This local GCP testing setup gives you confidence that your Terraform deployment will work correctly in the cloud! 🚀
