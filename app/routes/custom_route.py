"""Custom route class for timing requests."""

import time

from fastapi import Request, Response
from fastapi.routing import APIRoute


class TimedRoute(APIRoute):
    """
    Custom route class that adds timing information to responses.
    Adds an X-Response-Time header with the time taken to process the request.
    """

    def get_route_handler(self):
        original_route_handler = super().get_route_handler()

        async def custom_route_handler(request: Request) -> Response:
            before = time.time()
            response: Response = await original_route_handler(request)
            duration = time.time() - before
            response.headers["X-Response-Time"] = str(duration)
            return response

        return custom_route_handler
