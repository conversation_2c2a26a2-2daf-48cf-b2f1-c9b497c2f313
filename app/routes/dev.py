"""Development-only routes."""

import time
from datetime import datetime, timezone
from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session

from app.db import get_db
from core.config import settings
from core.models import GcpTokenModel, Neuron, NeuronModel
from core.services import TokenService

router = APIRouter(tags=["development"])


@router.post("/auth/dev-token")
async def get_dev_token(request: Request, db: Annotated[Session, Depends(get_db)]):
    """
    Development-only endpoint for getting a token without Bittensor authentication.
    Only available in development environment.
    """
    if settings.ENVIRONMENT != "development":
        raise HTTPException(status_code=404, detail="Not found")

    # Create a mock validator for development
    validator = Neuron(
        hotkey="5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi",
        uid=0,
        stake=1000.0,
        trust=0.9,
        is_validator=True
    )

    # Create token and store in database
    token = TokenService.create_token(validator, db)

    # Token is already stored in database by TokenService.create_token

    return {
        "access_token": token.access_token,
        "token_type": token.token_type,
        "expires_in": settings.TOKEN_EXPIRY,
    }


@router.post("/auth/dev-pubsub-token")
async def get_dev_pubsub_token(
    request: Request, db: Annotated[Session, Depends(get_db)]
):
    """
    Development-only endpoint for getting a Pub/Sub token without authentication.
    Only available in development environment.
    """
    if settings.ENVIRONMENT != "development":
        raise HTTPException(status_code=404, detail="Not found")

    # Create a mock validator for development
    validator = Neuron(
        hotkey="5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi",
        uid=0,
        stake=1000.0,
        trust=0.9,
    )

    # Make sure the validator exists in the database
    validator_db = (
        db.query(NeuronModel)
        .filter(NeuronModel.hotkey == validator.hotkey)
        .first()
    )

    if not validator_db:
        validator_db = NeuronModel(
            hotkey=validator.hotkey,
            uid=validator.uid,
            stake=validator.stake,
            trust=validator.trust,
        )
        db.add(validator_db)
        db.commit()

    try:
        # For development, return a mock Pub/Sub token since we're using emulator
        mock_token = "dev_pubsub_token_for_emulator_testing"
        expiry = int(time.time()) + 3600  # 1 hour from now

        # Record token issuance in database for consistency

        token_record = GcpTokenModel(
            hotkey=validator.hotkey,
            id=validator.uid,
            expires_at=datetime.fromtimestamp(expiry, tz=timezone.utc),
            scopes="https://www.googleapis.com/auth/pubsub",
            target_service_account="<EMAIL>",
        )
        db.add(token_record)
        db.commit()

        return {
            "access_token": mock_token,
            "expires_in": 3600,
            "token_type": "Bearer",
            "scope": "https://www.googleapis.com/auth/pubsub",
            "topic_paths": {
                "allocation_events": "projects/local-project/topics/allocation-events",
                "miner_events": "projects/local-project/topics/miner-events",
                "system_events": "projects/local-project/topics/system-events",
                "validation_events": "projects/local-project/topics/validation-events",
            },
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) from e
