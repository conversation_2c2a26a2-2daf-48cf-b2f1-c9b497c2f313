"""Neuron routes."""

from typing import Annotated

from fastapi import APIRouter, Depends

from app.auth import get_current_validator, get_neuron_from_token
from core.models import Neuron

router = APIRouter(tags=["validators"])


@router.get("/validators/me")
async def get_validator_info(
    validator: Annotated[N<PERSON><PERSON>, Depends(get_current_validator)],
):
    """
    Get information about the authenticated validator.
    Requires Bittensor signature authentication.
    """
    return {
        "hotkey": validator.hotkey,
        "uid": validator.uid,
        "stake": validator.stake,
        "trust": validator.trust,
    }


@router.get("/validators/me/token")
async def get_validator_info_from_token(
    validator: Annotated[N<PERSON><PERSON>, Depends(get_neuron_from_token)],
):
    """
    Get information about the authenticated validator.
    Requires JWT token authentication.
    """
    return {
        "hotkey": validator.hotkey,
        "uid": validator.uid,
        "stake": validator.stake,
        "trust": validator.trust,
    }
