"""Authentication routes."""

import time
from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session

from app.auth import get_current_validator, get_neuron_from_token
from app.db import get_db
from core.config import settings
from core.models import Neuron
from core.services import GoogleService, RateLimitService, TokenService

router = APIRouter(tags=["authentication"])


@router.post("/auth/token")
async def get_token(
    request: Request,
    validator: Annotated[Neuron, Depends(get_current_validator)],
    db: Annotated[Session, Depends(get_db)],
):
    """
    Get a JWT token for a validator.
    Requires Bittensor signature authentication.
    """
    # Rate limiting
    client_ip = request.client.host
    if not RateLimitService.check_rate_limit(db, client_ip, settings.RATE_LIMIT):
        raise HTTPException(status_code=429, detail="Rate limit exceeded")

    # Create token and store in database (handles token invalidation automatically)
    token = TokenService.create_token(validator, db)

    return {
        "access_token": token.access_token,
        "token_type": token.token_type,
        "expires_in": settings.TOKEN_EXPIRY,
    }


@router.post("/auth/pubsub-token")
async def get_pubsub_token(
    request: Request,
    validator: Annotated[Neuron, Depends(get_neuron_from_token)],
    db: Session = Depends(get_db),
):
    """
    Generate a short-lived OAuth2 token for Pub/Sub access.
    This endpoint requires a valid JWT token for authentication.
    """
    # Rate limiting
    client_ip = request.client.host
    if not RateLimitService.check_rate_limit(db, client_ip, settings.RATE_LIMIT):
        raise HTTPException(status_code=429, detail="Rate limit exceeded")

    try:
        # Generate Pub/Sub token
        token, expiry = GoogleService.generate_pubsub_token(validator.hotkey, db)

        return {
            "access_token": token,
            "expires_in": expiry - int(time.time()),
            "token_type": "Bearer",
            "scope": " ".join(GoogleService.PUBSUB_SCOPE),
            "topic_paths": GoogleService.get_pubsub_topic_paths(),
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) from e
    except RuntimeError as e:
        raise HTTPException(status_code=500, detail=str(e)) from e
