"""Routes package for the API."""

from fastapi import APIRouter

from app.routes.auth import router as auth_router
from app.routes.custom_route import TimedRoute
from app.routes.dev import router as dev_router
from app.routes.system import router as system_router
from app.routes.validators import router as validators_router

# Create a main router that includes all other routers
# Use the custom route class for timing
router = APIRouter(route_class=TimedRoute)

# Include all routers
router.include_router(auth_router)
router.include_router(validators_router)
router.include_router(dev_router)
router.include_router(system_router)
