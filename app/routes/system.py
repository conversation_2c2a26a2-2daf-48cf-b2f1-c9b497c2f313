"""System and observability routes."""

import time
from datetime import datetime, timezone

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from core import __version__
from core.config import settings
from core.services import GoogleService


class HealthResponse(BaseModel):
    """Health check response model."""

    status: str
    timestamp: str
    version: str
    environment: str
    uptime_seconds: float


class DetailedHealthResponse(BaseModel):
    """Detailed health check response model."""

    status: str
    timestamp: str
    version: str
    environment: str
    uptime_seconds: float
    services: dict
    config: dict


router = APIRouter(tags=["system"])

# Track startup time for uptime calculation
_startup_time = time.time()


@router.get("/health", response_model=HealthResponse)
async def health():
    """
    Simple health check endpoint for load balancers.
    Returns basic status information.
    """
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now(timezone.utc).isoformat(),
        version=__version__,
        environment=settings.ENVIRONMENT,
        uptime_seconds=round(time.time() - _startup_time, 2),
    )


@router.get("/system/health", response_model=DetailedHealthResponse)
async def detailed_health_check():
    """
    Detailed health check endpoint.
    Returns comprehensive status information including service checks.
    """
    # Basic health info
    current_time = datetime.now(timezone.utc)
    uptime = time.time() - _startup_time

    # Service checks
    services = {
        "database": await _check_database(),
        "config": _check_config(),
    }

    # Overall status
    overall_status = (
        "healthy"
        if all(service.get("status") == "healthy" for service in services.values())
        else "unhealthy"
    )

    return DetailedHealthResponse(
        status=overall_status,
        timestamp=current_time.isoformat(),
        version=__version__,
        environment=settings.ENVIRONMENT,
        uptime_seconds=round(uptime, 2),
        services=services,
        config={
            "environment": settings.ENVIRONMENT,
            "development_mode": settings.ENVIRONMENT == "development",
            "cors_enabled": bool(settings.ALLOWED_ORIGINS),
            "auth_enabled": True,
            "rate_limiting": True,
            "bittensor_network": settings.active_network,
            "production_network": settings.BITTENSOR_NETWORK,
            "netuid": settings.active_netuid,
            "production_netuid": settings.BITTENSOR_NETUID,
            "sandbox_netuid": settings.BITTENSOR_SANDBOX_NETUID,
        },
    )


async def _check_database() -> dict:
    """Check database connectivity."""
    try:
        import time

        from sqlalchemy import text

        from app.db import SessionLocal

        start_time = time.time()

        # Try to get a database session
        with SessionLocal() as session:
            # Simple query to test connectivity
            result = session.execute(text("SELECT 1"))
            result.fetchone()

        response_time = round((time.time() - start_time) * 1000, 2)

        return {
            "status": "healthy",
            "message": "Database connection successful",
            "response_time_ms": response_time,
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "message": f"Database connection failed: {str(e)}",
            "error": type(e).__name__,
        }


def _check_config() -> dict:
    """Check configuration validity."""
    try:
        # Check required configuration
        required_configs = ["DATABASE_URL", "JWT_SECRET"]

        missing_configs = []
        for config in required_configs:
            if not getattr(settings, config, None):
                missing_configs.append(config)

        if missing_configs:
            missing_list = ", ".join(missing_configs)
            return {
                "status": "unhealthy",
                "message": f"Missing required configuration: {missing_list}",
            }

        return {"status": "healthy", "message": "Configuration valid"}
    except Exception as e:
        return {
            "status": "unhealthy",
            "message": f"Configuration check failed: {str(e)}",
            "error": type(e).__name__,
        }


@router.get("/system/pubsub-status")
async def check_pubsub_status():
    """
    Check the status of the Pub/Sub setup.
    Only available in development environment.
    """
    if settings.ENVIRONMENT != "development":
        raise HTTPException(status_code=404, detail="Not found")

    results = GoogleService.verify_pubsub_setup()

    pubsub_status = "ok" if all(results.values()) else "error"

    return {
        "status": pubsub_status,
        "checks": results,
        "config": {
            "project_id": settings.GCP_PROJECT_ID,
            "pubsub_service_account": settings.GCP_PUBSUB_SERVICE_ACCOUNT,
            "pubsub_topics": {
                "allocation_events": settings.GCP_PUBSUB_ALLOCATION_EVENTS,
                "miner_events": settings.GCP_PUBSUB_MINER_EVENTS,
                "system_events": settings.GCP_PUBSUB_SYSTEM_EVENTS,
                "validation_events": settings.GCP_PUBSUB_VALIDATION_EVENTS,
            },
            "has_service_account_file": bool(settings.GCP_SERVICE_ACCOUNT_FILE),
            "has_service_account_json": bool(
                settings.GCP_SERVICE_ACCOUNT_JSON
                and settings.GCP_SERVICE_ACCOUNT_JSON != "{}"
            ),
        },
    }
