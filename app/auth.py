from datetime import datetime, timezone
from typing import Annotated

from fastapi import Depends, <PERSON><PERSON>, HTTPException
from sqlalchemy.orm import Session

from app.db import get_db
from core.config import settings
from core.models import NeuronModel
from core.services import BittensorService, NeuronService, TokenService


async def get_current_validator(
    db: Annotated[Session, Depends(get_db)],
    authorization: str = Header(None),
):
    """Authenticate a validator using Bittensor signature."""
    if not authorization:
        raise HTTPException(status_code=401, detail="Not authenticated")

    try:
        # Parse authorization header (format: "Bittensor <hotkey>:<signature>")
        parts = authorization.split()
        if len(parts) != 2 or parts[0] != "Bittensor":
            raise HTTPException(status_code=401, detail="Invalid authorization format")

        auth_parts = parts[1].split(":")
        if len(auth_parts) != 2:
            raise HTTPException(status_code=401, detail="Invalid credential format")

        ss58_address, signature = auth_parts

        # Get bittensor components
        subtensor, metagraph = BittensorService.get_components()

        # Check if the hotkey is a validator
        is_validator, uid, stake, trust = BittensorService.is_validator(
            metagraph, ss58_address
        )

        if not is_validator:
            raise HTTPException(
                status_code=403, detail="Not a validator hotkey (no stake)"
            )

        # Additional check: validators should have trust score
        if trust <= 0 and settings.REQUIRE_TRUST:
            raise HTTPException(status_code=403, detail="Validator has no trust score")

        # Create the message that the validator would have signed
        message = settings.auth_message

        # Verify the signature
        if not BittensorService.verify_signature(signature, message, ss58_address):
            raise HTTPException(status_code=401, detail="Invalid signature")

        # Get or create validator in the database
        validator_db = NeuronService.get_or_create_neuron(
            db, ss58_address, uid, stake, trust, is_validator
        )

        # Convert to Pydantic model
        validator = NeuronService.to_pydantic(validator_db)

        return validator
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=401, detail=f"Authentication failed: {str(e)}"
        ) from e


async def get_neuron_from_token(
    db: Annotated[Session, Depends(get_db)], authorization: str = Header(None)
):
    """Validate a JWT token and return the neuron."""
    if not authorization:
        raise HTTPException(status_code=401, detail="Not authenticated")

    try:
        # Parse authorization header (format: "Bearer <token>")
        parts = authorization.split()
        if len(parts) != 2 or parts[0].lower() != "bearer":
            raise HTTPException(status_code=401, detail="Invalid authorization format")

        token = parts[1]

        # Validate token
        is_valid, payload = TokenService.validate_token(token, db)

        if not is_valid:
            raise HTTPException(status_code=401, detail="Invalid or expired token")

        # Get neuron from database
        neuron_db = (
            db.query(NeuronModel)
            .filter(NeuronModel.hotkey == payload["sub"])
            .first()
        )

        if not neuron_db:
            raise HTTPException(status_code=401, detail="Neuron not found")

        # Update last_active timestamp
        neuron_db.last_active = datetime.now(tz=timezone.utc)
        db.commit()

        # Convert to Pydantic model
        neuron = NeuronService.to_pydantic(neuron_db)

        return neuron
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=401, detail=f"Authentication failed: {str(e)}"
        ) from e
