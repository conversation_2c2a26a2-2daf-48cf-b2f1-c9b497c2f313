import os

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from core.config import settings

# Check if we're in testing mode
if os.environ.get("TESTING") == "True":
    # Use SQLite for testing
    engine = create_engine(
        "sqlite:///:memory:", connect_args={"check_same_thread": False}
    )
else:
    # Use PostgreSQL for production
    engine = create_engine(settings.DATABASE_URL)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def create_tables():
    """Create all tables defined in models."""
    # Only import models when needed to avoid circular imports
    from core.models import Base

    Base.metadata.create_all(bind=engine)
