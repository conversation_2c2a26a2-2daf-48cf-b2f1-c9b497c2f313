FROM python:3.12-slim

WORKDIR /app

# Install uv
RUN pip install uv

# Copy dependency files for better layer caching
COPY pyproject.toml uv.lock ./

# Copy README.md (required by pyproject.toml)
COPY README.md ./

# Install dependencies (production only, frozen for reproducibility)
RUN uv sync --no-dev --frozen

# Copy application code
COPY . .

# Create a non-root user for security
RUN useradd --create-home --shell /bin/bash app && chown -R app:app /app
USER app

EXPOSE 8000

# Activate the virtual environment and run uvicorn directly
ENV PATH="/app/.venv/bin:$PATH"
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
