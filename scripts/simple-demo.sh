#!/bin/bash

# Simple Validator Demo Script
# This script demonstrates the validator ecosystem step by step

set -e

# Colors for output
BLUE='\033[36m'
GREEN='\033[32m'
YELLOW='\033[33m'
RED='\033[31m'
RESET='\033[0m'

echo -e "${BLUE}🎭 VALIDATOR TOKEN GATEWAY DEMO${RESET}"
echo "This demo shows validators authenticating and communicating via Pub/Sub"
echo ""

# Step 1: Setup environment
echo -e "${BLUE}📋 Step 1: Setting up environment...${RESET}"
export PUBSUB_EMULATOR_HOST=localhost:8085
export GCP_PROJECT_ID=local-project
export ENVIRONMENT=local-gcp

# Step 2: Start GCP emulators
echo -e "${BLUE}📋 Step 2: Starting GCP emulators...${RESET}"
make setup-local-gcp

# Step 3: Wait for API to be ready
echo -e "${BLUE}📋 Step 3: Waiting for API to be ready...${RESET}"
for i in {1..30}; do
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ API is ready!${RESET}"
        break
    fi
    echo "Waiting for API... ($i/30)"
    sleep 2
done

# Step 4: Test authentication
echo -e "${BLUE}📋 Step 4: Testing authentication...${RESET}"
echo "Testing dev token endpoint..."

# Test dev token endpoint
curl -X POST http://localhost:8000/dev/token \
  -H "Content-Type: application/json" \
  -d '{
    "hotkey": "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi",
    "signature": "test_signature",
    "message": "test_message",
    "timestamp": **********
  }' | jq '.' || echo "Authentication test completed"

echo ""

# Step 5: Test Pub/Sub connectivity
echo -e "${BLUE}📋 Step 5: Testing Pub/Sub connectivity...${RESET}"
python3 scripts/test-pubsub.py

echo ""

# Step 6: Show available topics
echo -e "${BLUE}📋 Step 6: Showing available topics...${RESET}"
python3 -c "
import os
os.environ['PUBSUB_EMULATOR_HOST'] = 'localhost:8085'
from google.cloud import pubsub_v1

publisher = pubsub_v1.PublisherClient()
project_path = 'projects/local-project'

try:
    topics = list(publisher.list_topics(request={'project': project_path}))
    print(f'📋 Available topics ({len(topics)}):')
    for topic in topics:
        topic_name = topic.name.split('/')[-1]
        print(f'  • {topic_name}')
except Exception as e:
    print(f'Error listing topics: {e}')
"

echo ""
echo -e "${GREEN}🎉 Demo setup complete!${RESET}"
echo ""
echo -e "${BLUE}🔗 Available Services:${RESET}"
echo "  📡 API Server: http://localhost:8000"
echo "  📚 API Docs: http://localhost:8000/docs"
echo "  📨 Pub/Sub Emulator: localhost:8085"
echo ""
echo -e "${BLUE}🧪 Next Steps:${RESET}"
echo "  1. Open a new terminal and run: make monitor-pubsub"
echo "  2. Open another terminal and run: make run-validator1"
echo "  3. Open another terminal and run: make run-validator2"
echo "  4. Watch the real-time communication!"
echo ""
echo -e "${YELLOW}💡 Tip: Keep this terminal open to maintain the services${RESET}"
echo -e "${YELLOW}    Press Ctrl+C to stop all services${RESET}"

# Keep services running
echo ""
echo -e "${BLUE}🔄 Services are running... Press Ctrl+C to stop${RESET}"

# Trap Ctrl+C to cleanup
trap 'echo -e "\n🛑 Stopping services..."; make clean-gcp; exit 0' INT

# Keep script running
while true; do
    sleep 10
    # Check if API is still running
    if ! curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo -e "${RED}⚠️  API seems to be down. Restarting...${RESET}"
        break
    fi
done
