#!/bin/bash

# Script to manually trigger the cleanup endpoint
# This is useful for testing the Cloud Function cleanup endpoint

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧹 Validator Token Gateway - Manual Cleanup Trigger${NC}"
echo

# Check if environment is provided (for endpoint selection only)
ENVIRONMENT=${1:-"production"}

if [ "$ENVIRONMENT" != "production" ] && [ "$ENVIRONMENT" != "development" ]; then
    echo -e "${RED}❌ Invalid environment. Use 'production' or 'development'${NC}"
    echo "Usage: $0 [production|development]"
    exit 1
fi

echo -e "${YELLOW}Endpoint: $ENVIRONMENT${NC}"
echo -e "${BLUE}Note: Cleanup operates across ALL environments regardless of endpoint used${NC}"

# Get the function URL from Terraform outputs
echo -e "${BLUE}📡 Getting Cloud Function URL...${NC}"
cd terraform

if ! command -v terraform &> /dev/null; then
    echo -e "${RED}❌ Terraform not found. Please install Terraform.${NC}"
    exit 1
fi

# Get the cleanup endpoint URL
if [ "$ENVIRONMENT" = "production" ]; then
    CLEANUP_URL=$(terraform output -raw cleanup_endpoints | jq -r '.production')
else
    CLEANUP_URL=$(terraform output -raw cleanup_endpoints | jq -r '.development')
fi

if [ "$CLEANUP_URL" = "null" ] || [ -z "$CLEANUP_URL" ]; then
    echo -e "${RED}❌ No cleanup URL found for $ENVIRONMENT environment${NC}"
    echo "Make sure the Cloud Function is deployed for this environment."
    exit 1
fi

echo -e "${GREEN}✅ Found cleanup URL: $CLEANUP_URL${NC}"

# Get service account for authentication
SCHEDULER_SA=$(terraform output -raw scheduler_service_account)
echo -e "${YELLOW}Using service account: $SCHEDULER_SA${NC}"

cd ..

# Generate access token for the service account
echo -e "${BLUE}🔑 Generating access token...${NC}"
ACCESS_TOKEN=$(gcloud auth print-access-token --impersonate-service-account="$SCHEDULER_SA")

if [ -z "$ACCESS_TOKEN" ]; then
    echo -e "${RED}❌ Failed to generate access token${NC}"
    echo "Make sure you have permission to impersonate the service account."
    exit 1
fi

echo -e "${GREEN}✅ Access token generated${NC}"

# Call the cleanup endpoint
echo -e "${BLUE}🧹 Triggering cleanup...${NC}"
echo "URL: $CLEANUP_URL"
echo

RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}\n" \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    -H "Content-Type: application/json" \
    -X GET \
    "$CLEANUP_URL")

# Extract HTTP status and response body
HTTP_STATUS=$(echo "$RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
RESPONSE_BODY=$(echo "$RESPONSE" | sed '/HTTP_STATUS:/d')

echo -e "${YELLOW}Response Status: $HTTP_STATUS${NC}"
echo -e "${YELLOW}Response Body:${NC}"
echo "$RESPONSE_BODY" | jq . 2>/dev/null || echo "$RESPONSE_BODY"

if [ "$HTTP_STATUS" = "200" ]; then
    echo -e "\n${GREEN}✅ Cleanup completed successfully!${NC}"

    # Parse and display cleanup results
    if echo "$RESPONSE_BODY" | jq . >/dev/null 2>&1; then
        echo -e "\n${BLUE}📊 Cleanup Results:${NC}"
        echo "$RESPONSE_BODY" | jq -r '
            "Environment: " + .environment,
            "Timestamp: " + (.timestamp | tostring),
            "Pub/Sub tokens cleaned: " + (.pubsub_tokens_cleaned | tostring),
            "JWT tokens cleaned: " + (.jwt_tokens_cleaned | tostring),
            "Rate limits cleaned: " + (.rate_limits_cleaned | tostring)
        '
    fi
else
    echo -e "\n${RED}❌ Cleanup failed with status $HTTP_STATUS${NC}"
    exit 1
fi

echo -e "\n${GREEN}🎉 Manual cleanup trigger completed!${NC}"
