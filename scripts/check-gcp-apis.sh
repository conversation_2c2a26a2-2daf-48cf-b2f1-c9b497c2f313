#!/bin/bash

# Simple GCP API Checker
# Validates that required Google Cloud APIs are enabled

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Checking Google Cloud APIs for Validator Token Gateway...${NC}"

# Get project ID
if [ -f "terraform/terraform.tfvars" ]; then
    PROJECT_ID=$(grep "project_id" terraform/terraform.tfvars | cut -d'"' -f2)
    echo -e "${YELLOW}📋 Project: $PROJECT_ID${NC}"
else
    echo -e "${RED}❌ terraform/terraform.tfvars not found${NC}"
    exit 1
fi

# Get all enabled APIs
echo -e "${YELLOW}🔍 Getting enabled APIs...${NC}"
ENABLED_APIS=$(gcloud services list --enabled --format="value(name)" 2>/dev/null)

# Required APIs for the Validator Token Gateway
REQUIRED_APIS=(
    "cloudfunctions.googleapis.com"
    "sql-component.googleapis.com"
    "sqladmin.googleapis.com"
    "secretmanager.googleapis.com"
    "pubsub.googleapis.com"
    "storage.googleapis.com"
    "cloudbuild.googleapis.com"
    "compute.googleapis.com"
    "servicenetworking.googleapis.com"
)

# Check function
check_api() {
    local api=$1
    if echo "$ENABLED_APIS" | grep -q "/$api$"; then
        echo -e "${GREEN}✅ $api${NC}"
        return 0
    else
        echo -e "${RED}❌ $api${NC}"
        return 1
    fi
}

# Check all required APIs
echo -e "\n${BLUE}🔧 Required APIs Status:${NC}"
missing_count=0
missing_apis=()

for api in "${REQUIRED_APIS[@]}"; do
    if ! check_api "$api"; then
        ((missing_count++))
        missing_apis+=("$api")
    fi
done

# Summary
echo -e "\n${BLUE}📊 Summary:${NC}"
enabled_count=$((${#REQUIRED_APIS[@]} - missing_count))
echo "Required APIs: $enabled_count/${#REQUIRED_APIS[@]} enabled"

if [ $missing_count -eq 0 ]; then
    echo -e "${GREEN}🎉 All required APIs are enabled!${NC}"
    echo -e "${GREEN}✅ Ready for deployment${NC}"
else
    echo -e "\n${RED}❌ Missing APIs (${missing_count}):${NC}"
    for api in "${missing_apis[@]}"; do
        echo "  - $api"
    done
    
    echo -e "\n${YELLOW}💡 To enable missing APIs:${NC}"
    echo -e "${BLUE}gcloud services enable ${missing_apis[*]}${NC}"
    
    echo -e "\n${YELLOW}🤖 Enable them now? (y/N)${NC}"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}🔧 Enabling APIs...${NC}"
        if gcloud services enable "${missing_apis[@]}"; then
            echo -e "${GREEN}✅ APIs enabled successfully!${NC}"
        else
            echo -e "${RED}❌ Failed to enable some APIs${NC}"
            exit 1
        fi
    fi
fi

echo -e "${GREEN}✅ API check complete!${NC}"
