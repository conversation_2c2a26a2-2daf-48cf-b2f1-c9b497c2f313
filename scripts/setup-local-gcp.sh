#!/bin/bash

# Setup Local GCP Environment for Validator Token Gateway
# This script sets up local GCP emulators for testing

set -e

echo "🚀 Setting up Local GCP Environment for Validator Token Gateway"

# Colors for output
BLUE='\033[36m'
GREEN='\033[32m'
YELLOW='\033[33m'
RED='\033[31m'
RESET='\033[0m'

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running. Please start Docker first.${RESET}"
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ docker-compose is not installed.${RESET}"
    exit 1
fi

echo -e "${BLUE}📋 Checking prerequisites...${RESET}"

# Create scripts directory if it doesn't exist
mkdir -p scripts

# Create local service account if it doesn't exist
if [ ! -f "local-service-account.json" ]; then
    echo -e "${YELLOW}⚠️  local-service-account.json not found. Creating mock service account...${RESET}"
    # This would be created by the save-file command above
fi

echo -e "${BLUE}🐳 Starting GCP emulators...${RESET}"

# Start base services first
docker-compose up -d db redis

# Wait for base services
echo -e "${BLUE}⏳ Waiting for base services to be ready...${RESET}"
sleep 10

# Start GCP emulators
docker-compose -f docker-compose.yml -f docker-compose.local-gcp.yml up -d

echo -e "${BLUE}⏳ Waiting for emulators to start...${RESET}"
sleep 15

# Verify Pub/Sub emulator
echo -e "${BLUE}🔍 Verifying Pub/Sub emulator...${RESET}"
if curl -s http://localhost:8085 > /dev/null; then
    echo -e "${GREEN}✅ Pub/Sub emulator is running${RESET}"
else
    echo -e "${RED}❌ Pub/Sub emulator failed to start${RESET}"
fi

# Verify Secret Manager mock
echo -e "${BLUE}🔍 Verifying Secret Manager mock...${RESET}"
if docker-compose exec -T secret-manager-mock redis-cli ping > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Secret Manager mock is running${RESET}"
else
    echo -e "${RED}❌ Secret Manager mock failed to start${RESET}"
fi

# Test Pub/Sub functionality
echo -e "${BLUE}🧪 Testing Pub/Sub functionality...${RESET}"

# Use the Python test script
if command -v python3 &> /dev/null; then
    python3 scripts/test-pubsub.py
else
    echo -e "${YELLOW}⚠️  Python3 not found, skipping Pub/Sub test${RESET}"
fi

echo -e "${GREEN}🎉 Local GCP environment setup complete!${RESET}"
echo ""
echo -e "${BLUE}📊 Service Status:${RESET}"
echo "  🗄️  PostgreSQL: localhost:5432"
echo "  🔴 Redis: localhost:6379"
echo "  📨 Pub/Sub Emulator: localhost:8085"
echo "  🔐 Secret Manager Mock: localhost:6380"
echo "  ☁️  Cloud SQL Mock: localhost:5433"
echo "  💾 Memorystore Mock: localhost:6381"
echo ""
echo -e "${BLUE}🔗 API Endpoints:${RESET}"
echo "  📡 API Server: http://localhost:8000"
echo "  📚 API Docs: http://localhost:8000/docs"
echo ""
echo -e "${BLUE}🧪 Testing Commands:${RESET}"
echo "  make test-local-gcp     # Run tests against local GCP"
echo "  make logs-gcp           # View GCP emulator logs"
echo "  make clean-gcp          # Clean up GCP emulators"
echo ""
echo -e "${YELLOW}💡 Tip: Use 'make docker-logs' to monitor all services${RESET}"
