#!/bin/bash
set -e

# This script prepares the source code for the Cloud Function by packaging
# the function-specific code with the shared 'core' module.

SCRIPT_DIR=$(dirname "$0")
PROJECT_ROOT=$(realpath "$SCRIPT_DIR/..")
SOURCE_DIR="/tmp/function-package"

echo "Preparing function source in $SOURCE_DIR..."

# Clean up previous package
rm -rf "$SOURCE_DIR"

# Create package directory
mkdir -p "$SOURCE_DIR"

# Copy function source and core module
echo "Copying function files..."
cp -r "$PROJECT_ROOT/functions/auth_function/"* "$SOURCE_DIR/"

echo "Copying core module..."
cp -r "$PROJECT_ROOT/core" "$SOURCE_DIR/"

echo "✅ Function source prepared successfully."
