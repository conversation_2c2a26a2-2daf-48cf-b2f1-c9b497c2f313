#!/bin/bash

# Test packaging script to verify core module is included
# This script tests the packaging logic without deploying

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Testing Cloud Function packaging...${NC}"

# Configuration
FUNCTION_DIR="functions/auth_function"
TEMP_DIR="/tmp/function-package-test"
ZIP_FILE="test-function-source.zip"

# Clean up any existing temp directory
echo -e "${YELLOW}🧹 Cleaning up previous test...${NC}"
rm -rf "$TEMP_DIR"
mkdir -p "$TEMP_DIR"

# Copy function files
echo -e "${YELLOW}📦 Copying function files...${NC}"
cp -r "$FUNCTION_DIR"/* "$TEMP_DIR/"

# Copy core module
echo -e "${YELLOW}📦 Including core module...${NC}"
if [ -d "core" ]; then
    cp -r "core" "$TEMP_DIR/"
    echo -e "${GREEN}✅ Core module copied${NC}"
else
    echo -e "${RED}❌ Core module not found!${NC}"
    exit 1
fi

# Create zip file
echo -e "${YELLOW}📦 Creating zip file...${NC}"
cd "$TEMP_DIR"
zip -r "$ZIP_FILE" . -x "*.pyc" "*__pycache__*" "*.git*" > /dev/null

# Verify contents
echo -e "${YELLOW}🔍 Verifying package contents...${NC}"
echo "Package contents:"
unzip -l "$ZIP_FILE" | grep -E "(main.py|core/|requirements.txt)"

# Check for critical files
echo -e "\n${YELLOW}🔍 Checking for critical files...${NC}"

critical_files=(
    "main.py"
    "requirements.txt"
    "core/__init__.py"
    "core/config.py"
    "core/services/__init__.py"
    "core/services/auth_service.py"
    "core/services/token_service.py"
    "core/services/google_service.py"
    "core/services/rate_limit_service.py"
)

all_good=true
for file in "${critical_files[@]}"; do
    if unzip -l "$ZIP_FILE" | grep -q "$file"; then
        echo -e "${GREEN}✅ $file${NC}"
    else
        echo -e "${RED}❌ $file (MISSING!)${NC}"
        all_good=false
    fi
done

# Test import simulation
echo -e "\n${YELLOW}🔍 Testing import paths...${NC}"
cd "$TEMP_DIR"

# Check if we can find the core module
if [ -d "core" ] && [ -f "core/__init__.py" ]; then
    echo -e "${GREEN}✅ Core module structure is correct${NC}"
else
    echo -e "${RED}❌ Core module structure is incorrect${NC}"
    all_good=false
fi

# Check if main.py exists and has the right imports
if [ -f "main.py" ]; then
    if grep -q "from core.config import settings" main.py; then
        echo -e "${GREEN}✅ Core imports found in main.py${NC}"
    else
        echo -e "${RED}❌ Core imports not found in main.py${NC}"
        all_good=false
    fi
else
    echo -e "${RED}❌ main.py not found${NC}"
    all_good=false
fi

# Final result
echo -e "\n${BLUE}📊 Test Results:${NC}"
if [ "$all_good" = true ]; then
    echo -e "${GREEN}🎉 All tests passed! Package is ready for deployment.${NC}"
    echo -e "${GREEN}✅ Core module is properly included${NC}"
    echo -e "${GREEN}✅ All critical files are present${NC}"
    echo -e "${GREEN}✅ Import structure is correct${NC}"
else
    echo -e "${RED}❌ Some tests failed! Package needs fixes.${NC}"
    exit 1
fi

# Cleanup
echo -e "\n${YELLOW}🧹 Cleaning up test files...${NC}"
cd ..
rm -rf "$TEMP_DIR"

echo -e "${GREEN}✅ Packaging test complete!${NC}"
