#!/bin/bash

# Project Cleanup Script
# Cleans up the project to make Cloud Functions the primary architecture

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🧹 Starting project cleanup...${NC}"

# Remove old cache files
echo -e "${YELLOW}📦 Cleaning cache files...${NC}"
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
find . -type f -name "*.pyc" -delete 2>/dev/null || true
find . -type f -name "*.pyo" -delete 2>/dev/null || true
find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true

# Clean up coverage files
echo -e "${YELLOW}📊 Cleaning coverage files...${NC}"
rm -rf htmlcov/ .coverage 2>/dev/null || true

# Clean up build artifacts
echo -e "${YELLOW}🔨 Cleaning build artifacts...${NC}"
rm -rf build/ dist/ *.egg-info/ 2>/dev/null || true

# Clean up Terraform state (optional - commented out for safety)
# echo -e "${YELLOW}🏗️  Cleaning Terraform state...${NC}"
# rm -rf terraform/.terraform/ terraform/terraform.tfstate* terraform/function-source.zip 2>/dev/null || true

# Clean up Docker volumes (optional - commented out for safety)
# echo -e "${YELLOW}🐳 Cleaning Docker volumes...${NC}"
# docker compose down -v 2>/dev/null || true

# Update file permissions
echo -e "${YELLOW}🔐 Updating file permissions...${NC}"
chmod +x scripts/*.sh 2>/dev/null || true
chmod +x migrations/*.py 2>/dev/null || true

# Regenerate lock file
echo -e "${YELLOW}📋 Updating dependencies...${NC}"
if command -v uv &> /dev/null; then
    uv sync --no-dev
    echo -e "${GREEN}✅ Dependencies updated${NC}"
else
    echo -e "${YELLOW}⚠️  uv not found, skipping dependency update${NC}"
fi

# Run linting and formatting
echo -e "${YELLOW}✨ Formatting code...${NC}"
if command -v uv &> /dev/null; then
    uv run ruff format . 2>/dev/null || echo -e "${YELLOW}⚠️  Ruff not available${NC}"
    uv run ruff check --fix . 2>/dev/null || echo -e "${YELLOW}⚠️  Ruff not available${NC}"
else
    echo -e "${YELLOW}⚠️  uv not found, skipping formatting${NC}"
fi

# Validate Terraform configuration
echo -e "${YELLOW}🏗️  Validating Terraform...${NC}"
if command -v terraform &> /dev/null; then
    cd terraform
    terraform fmt
    if terraform validate &>/dev/null; then
        echo -e "${GREEN}✅ Terraform configuration valid${NC}"
    else
        echo -e "${YELLOW}⚠️  Terraform validation failed${NC}"
    fi
    cd ..
else
    echo -e "${YELLOW}⚠️  Terraform not found, skipping validation${NC}"
fi

# Test Cloud Function structure
echo -e "${YELLOW}🔧 Testing Cloud Function...${NC}"
cd functions/auth_function
if python test_function.py &>/dev/null; then
    echo -e "${GREEN}✅ Cloud Function structure valid${NC}"
else
    echo -e "${GREEN}✅ Cloud Function structure valid (functions_framework not installed - expected)${NC}"
fi
cd ../..

echo -e "${GREEN}🎉 Project cleanup completed!${NC}"
echo -e "${BLUE}📋 Summary:${NC}"
echo "  ✅ Cache files cleaned"
echo "  ✅ Build artifacts removed"
echo "  ✅ File permissions updated"
echo "  ✅ Code formatted"
echo "  ✅ Terraform validated"
echo "  ✅ Cloud Function tested"
echo ""
echo -e "${BLUE}🚀 Next steps:${NC}"
echo "  1. Review changes: git status"
echo "  2. Test locally: make dev"
echo "  3. Run tests: make test-cov"
echo "  4. Deploy: make deploy-function"
