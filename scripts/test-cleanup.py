#!/usr/bin/env python3
"""
Test script for the cleanup functionality.

This script tests the cleanup functions locally and can also be used to
manually trigger cleanup operations.
"""

import os
import sys
from datetime import datetime, timedelta, timezone

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from core.config import settings
from core.models import GcpTokenModel, RateLimitModel, TokenModel
from core.services import GoogleService, RateLimitService, TokenService


def create_test_data(db):
    """Create some test expired tokens for cleanup testing."""
    print("🧪 Creating test expired tokens...")

    # Create expired Pub/Sub token
    expired_pubsub = GcpTokenModel(
        hotkey="test_hotkey_1",
        environment=settings.ENVIRONMENT,
        uid=1,
        expires_at=datetime.now(tz=timezone.utc) - timedelta(hours=2),  # Expired 2 hours ago
        scopes="https://www.googleapis.com/auth/pubsub",
        target_service_account="<EMAIL>"
    )
    db.add(expired_pubsub)

    # Create expired JWT token
    expired_jwt = TokenModel(
        hotkey="test_hotkey_2",
        environment=settings.ENVIRONMENT,
        uid=2,
        expires_at=datetime.now(tz=timezone.utc) - timedelta(hours=1),  # Expired 1 hour ago
        access_token="expired_jwt_token",
        token_type="bearer"
    )
    db.add(expired_jwt)

    # Create old rate limit record
    old_rate_limit = RateLimitModel(
        client_ip="*************",
        environment=settings.ENVIRONMENT,
        window_start=datetime.now(tz=timezone.utc) - timedelta(hours=25),  # 25 hours old
        request_count=10
    )
    db.add(old_rate_limit)

    db.commit()
    print("✅ Test data created")


def test_cleanup_functions(db):
    """Test all cleanup functions."""
    print("\n🧹 Testing cleanup functions...")

    # Test Pub/Sub token cleanup
    print("\n📡 Testing Pub/Sub token cleanup...")
    pubsub_cleaned = GoogleService.cleanup_expired_pubsub_tokens(db)
    print(f"   Cleaned up {pubsub_cleaned} expired Pub/Sub tokens")

    # Test JWT token cleanup
    print("\n🔑 Testing JWT token cleanup...")
    jwt_cleaned = TokenService.cleanup_expired_tokens(db)
    print(f"   Cleaned up {jwt_cleaned} expired JWT tokens")

    # Test rate limit cleanup
    print("\n⏱️  Testing rate limit cleanup...")
    rate_limits_cleaned = RateLimitService.cleanup_old_rate_limits(db, hours_old=24)
    print(f"   Cleaned up {rate_limits_cleaned} old rate limit records")

    return {
        "pubsub_tokens_cleaned": pubsub_cleaned,
        "jwt_tokens_cleaned": jwt_cleaned,
        "rate_limits_cleaned": rate_limits_cleaned
    }


def count_records(db):
    """Count current records in each table across all environments."""
    pubsub_count = db.query(GcpTokenModel).count()
    jwt_count = db.query(TokenModel).count()
    rate_limit_count = db.query(RateLimitModel).count()

    return {
        "pubsub_tokens": pubsub_count,
        "jwt_tokens": jwt_count,
        "rate_limits": rate_limit_count
    }


def main():
    """Main test function."""
    print("🧪 Validator Token Gateway - Cleanup Test")
    print(f"Current Environment: {settings.ENVIRONMENT}")
    print(f"Database: {settings.DATABASE_URL}")
    print("Note: Cleanup operates across ALL environments")
    print()

    # Create database connection
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    db = SessionLocal()
    try:
        # Count initial records
        print("📊 Initial record counts:")
        initial_counts = count_records(db)
        for table, count in initial_counts.items():
            print(f"   {table}: {count}")

        # Create test data
        create_test_data(db)

        # Count after adding test data
        print("\n📊 Record counts after adding test data:")
        after_test_counts = count_records(db)
        for table, count in after_test_counts.items():
            print(f"   {table}: {count}")

        # Run cleanup
        cleanup_results = test_cleanup_functions(db)

        # Count final records
        print("\n📊 Final record counts:")
        final_counts = count_records(db)
        for table, count in final_counts.items():
            print(f"   {table}: {count}")

        # Summary
        print("\n📋 Cleanup Summary:")
        print(f"   Environment: {settings.ENVIRONMENT}")
        print(f"   Timestamp: {datetime.now(tz=timezone.utc).isoformat()}")
        for cleanup_type, count in cleanup_results.items():
            print(f"   {cleanup_type}: {count}")

        print("\n✅ Cleanup test completed successfully!")

    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        return 1
    finally:
        db.close()

    return 0


if __name__ == "__main__":
    sys.exit(main())
