#!/bin/bash

# Validate Environment Configuration Script
# Ensures Bittensor network settings align with environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Validating Environment Configuration...${NC}"

# Read Terraform variables
if [ -f "terraform/terraform.tfvars" ]; then
    echo -e "${YELLOW}📋 Reading Terraform configuration...${NC}"

    # Extract values from terraform.tfvars
    PROJECT_ID=$(grep "project_id" terraform/terraform.tfvars | cut -d'"' -f2)
    ENVIRONMENT=$(grep "environment" terraform/terraform.tfvars | cut -d'"' -f2)
    BITTENSOR_NETWORK=$(grep "bittensor_network" terraform/terraform.tfvars | cut -d'"' -f2)
    BITTENSOR_NETUID=$(grep "bittensor_netuid" terraform/terraform.tfvars | grep -v sandbox | awk '{print $3}')
    BITTENSOR_SANDBOX_NETUID=$(grep "bittensor_sandbox_netuid" terraform/terraform.tfvars | awk '{print $3}')

    echo "Project ID: $PROJECT_ID"
    echo "Environment: $ENVIRONMENT"
    echo "Bittensor Network: $BITTENSOR_NETWORK"
    echo "Bittensor NetUID: $BITTENSOR_NETUID"
    echo "Bittensor Sandbox NetUID: $BITTENSOR_SANDBOX_NETUID"
else
    echo -e "${RED}❌ terraform/terraform.tfvars not found${NC}"
    exit 1
fi

echo -e "\n${YELLOW}🔍 Validating configuration alignment...${NC}"

# Validation logic
all_good=true

# Check production configuration
if [ "$ENVIRONMENT" = "production" ]; then
    echo -e "${BLUE}🏭 Production Environment Validation:${NC}"

    if [ "$BITTENSOR_NETWORK" = "finney" ]; then
        echo -e "${GREEN}✅ Network: finney (correct for production)${NC}"
    else
        echo -e "${RED}❌ Network: $BITTENSOR_NETWORK (should be 'finney' for production)${NC}"
        all_good=false
    fi

    if [ "$BITTENSOR_NETUID" = "27" ]; then
        echo -e "${GREEN}✅ NetUID: 27 (correct for production)${NC}"
    else
        echo -e "${RED}❌ NetUID: $BITTENSOR_NETUID (should be '27' for production)${NC}"
        all_good=false
    fi

    if [ "$BITTENSOR_SANDBOX_NETUID" = "15" ]; then
        echo -e "${GREEN}✅ Sandbox NetUID: 15 (correct)${NC}"
    else
        echo -e "${RED}❌ Sandbox NetUID: $BITTENSOR_SANDBOX_NETUID (should be '15')${NC}"
        all_good=false
    fi

# Check development configuration
elif [ "$ENVIRONMENT" = "development" ]; then
    echo -e "${BLUE}🧪 Development Environment Validation:${NC}"

    if [ "$BITTENSOR_NETWORK" = "test" ]; then
        echo -e "${GREEN}✅ Network: test (correct for development)${NC}"
    else
        echo -e "${RED}❌ Network: $BITTENSOR_NETWORK (should be 'test' for development)${NC}"
        all_good=false
    fi

    if [ "$BITTENSOR_NETUID" = "15" ]; then
        echo -e "${GREEN}✅ NetUID: 15 (correct for development)${NC}"
    else
        echo -e "${RED}❌ NetUID: $BITTENSOR_NETUID (should be '15' for development)${NC}"
        all_good=false
    fi

    if [ "$BITTENSOR_SANDBOX_NETUID" = "15" ]; then
        echo -e "${GREEN}✅ Sandbox NetUID: 15 (correct)${NC}"
    else
        echo -e "${RED}❌ Sandbox NetUID: $BITTENSOR_SANDBOX_NETUID (should be '15')${NC}"
        all_good=false
    fi

else
    echo -e "${YELLOW}⚠️  Unknown environment: $ENVIRONMENT${NC}"
fi

# Check project ID
echo -e "\n${BLUE}🏗️  Project Configuration:${NC}"
if [ "$PROJECT_ID" = "ni-sn27-frontend-dev" ]; then
    echo -e "${GREEN}✅ Project ID: $PROJECT_ID (correct)${NC}"
else
    echo -e "${YELLOW}⚠️  Project ID: $PROJECT_ID (verify this is correct)${NC}"
fi

# Summary
echo -e "\n${BLUE}📊 Configuration Summary:${NC}"
echo "┌─────────────────────┬─────────────────────┬─────────────────────┐"
echo "│ Environment         │ Expected            │ Actual              │"
echo "├─────────────────────┼─────────────────────┼─────────────────────┤"
if [ "$ENVIRONMENT" = "production" ]; then
    printf "│ %-19s │ %-19s │ %-19s │\n" "Production" "finney/27" "$BITTENSOR_NETWORK/$BITTENSOR_NETUID"
elif [ "$ENVIRONMENT" = "development" ]; then
    printf "│ %-19s │ %-19s │ %-19s │\n" "Development" "test/15" "$BITTENSOR_NETWORK/$BITTENSOR_NETUID"
else
    printf "│ %-19s │ %-19s │ %-19s │\n" "$ENVIRONMENT" "unknown" "$BITTENSOR_NETWORK/$BITTENSOR_NETUID"
fi
echo "└─────────────────────┴─────────────────────┴─────────────────────┘"

# Final result
echo -e "\n${BLUE}🎯 Validation Results:${NC}"
if [ "$all_good" = true ]; then
    echo -e "${GREEN}🎉 All configurations are correctly aligned!${NC}"
    echo -e "${GREEN}✅ Environment and Bittensor settings match${NC}"
    echo -e "${GREEN}✅ Ready for deployment${NC}"
else
    echo -e "${RED}❌ Configuration misalignment detected!${NC}"
    echo -e "${YELLOW}💡 Recommended fixes:${NC}"

    if [ "$ENVIRONMENT" = "production" ]; then
        echo "  - Set bittensor_network = \"finney\""
        echo "  - Set bittensor_netuid = 27"
    elif [ "$ENVIRONMENT" = "development" ]; then
        echo "  - Set bittensor_network = \"test\""
        echo "  - Set bittensor_netuid = 15"
    fi

    echo -e "${YELLOW}📝 Edit terraform/terraform.tfvars to fix these issues${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Environment validation complete!${NC}"
