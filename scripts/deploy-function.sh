#!/bin/bash

# Deploy Cloud Function script
# This script packages and deploys the auth function to Google Cloud

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
FUNCTION_NAME="validator-token-gateway-auth"
FUNCTION_DIR="functions/auth_function"
TEMP_DIR="/tmp/function-deploy"
ZIP_FILE="function-source.zip"

# Check if required environment variables are set
check_env_vars() {
    echo -e "${YELLOW}🔍 Checking environment variables...${NC}"

    required_vars=("GCP_PROJECT_ID" "GCP_REGION")
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            echo -e "${RED}❌ Environment variable $var is not set${NC}"
            exit 1
        fi
    done

    echo -e "${GREEN}✅ Environment variables OK${NC}"
}

# Package the function
package_function() {
    echo -e "${YELLOW}📦 Packaging function...${NC}"

    # Clean up any existing temp directory
    rm -rf "$TEMP_DIR"
    mkdir -p "$TEMP_DIR"

    # Copy function files
    cp -r "$FUNCTION_DIR"/* "$TEMP_DIR/"

    # Copy core module (CRITICAL: Required for function to work)
    echo -e "${YELLOW}📦 Including core module...${NC}"
    cp -r "core" "$TEMP_DIR/"

    # Create zip file
    cd "$TEMP_DIR"
    zip -r "$ZIP_FILE" . -x "*.pyc" "*__pycache__*" "*.git*"

    echo -e "${GREEN}✅ Function packaged as $ZIP_FILE with core module${NC}"
}

# Upload to Cloud Storage
upload_to_storage() {
    echo -e "${YELLOW}☁️  Uploading to Cloud Storage...${NC}"

    BUCKET_NAME="${GCP_PROJECT_ID}-${FUNCTION_NAME}-source"

    # Create bucket if it doesn't exist
    if ! gsutil ls "gs://$BUCKET_NAME" &> /dev/null; then
        echo "Creating storage bucket: $BUCKET_NAME"
        gsutil mb -p "$GCP_PROJECT_ID" -l "$GCP_REGION" "gs://$BUCKET_NAME"
    fi

    # Upload the zip file
    gsutil cp "$ZIP_FILE" "gs://$BUCKET_NAME/"

    echo -e "${GREEN}✅ Function uploaded to gs://$BUCKET_NAME/$ZIP_FILE${NC}"
}

# Deploy with Terraform
deploy_with_terraform() {
    echo -e "${YELLOW}🚀 Deploying with Terraform...${NC}"

    cd terraform

    # Initialize Terraform if needed
    if [ ! -d ".terraform" ]; then
        terraform init
    fi

    # Plan the deployment
    terraform plan -out=tfplan

    # Apply the deployment
    terraform apply tfplan

    echo -e "${GREEN}✅ Terraform deployment complete${NC}"
}

# Run database migration (optional for first-time deployment)
run_migration() {
    echo -e "${YELLOW}🗄️  Running database migration...${NC}"
    echo -e "${YELLOW}ℹ️  Note: This step creates tables if they don't exist (safe for first-time deployment)${NC}"

    cd ..
    python migrations/add_rate_limiting_table.py

    echo -e "${GREEN}✅ Database migration complete${NC}"
}

# Test the deployed function
test_function() {
    echo -e "${YELLOW}🧪 Testing deployed function...${NC}"

    # Get function URL from Terraform output
    cd terraform
    FUNCTION_URL=$(terraform output -raw function_url 2>/dev/null || echo "")

    if [ -z "$FUNCTION_URL" ]; then
        echo -e "${RED}❌ Could not get function URL from Terraform${NC}"
        return 1
    fi

    echo "Function URL: $FUNCTION_URL"

    # Test that GET requests are properly rejected
    echo "Testing that GET requests are rejected..."
    response=$(curl -s -o /dev/null -w "%{http_code}" -X GET "$FUNCTION_URL/auth/token")

    if [ "$response" = "405" ]; then
        echo -e "${GREEN}✅ Function is responding correctly (GET rejected)${NC}"
    else
        echo -e "${RED}❌ Function test failed (HTTP $response, expected 405)${NC}"
        return 1
    fi
}

# Cleanup
cleanup() {
    echo -e "${YELLOW}🧹 Cleaning up...${NC}"
    rm -rf "$TEMP_DIR"
    echo -e "${GREEN}✅ Cleanup complete${NC}"
}

# Validate environment configuration
validate_environment() {
    echo -e "${YELLOW}🔍 Validating environment configuration...${NC}"

    if [ -f "scripts/validate-environment.sh" ]; then
        chmod +x scripts/validate-environment.sh
        if ./scripts/validate-environment.sh; then
            echo -e "${GREEN}✅ Environment validation passed${NC}"
        else
            echo -e "${RED}❌ Environment validation failed${NC}"
            exit 1
        fi
    else
        echo -e "${YELLOW}⚠️  Environment validation script not found, skipping...${NC}"
    fi
}

# Main deployment process
main() {
    echo -e "${GREEN}🚀 Starting Cloud Function deployment...${NC}"

    check_env_vars
    validate_environment
    run_migration
    deploy_with_terraform
    test_function

    echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
    echo -e "${YELLOW}📝 Next steps:${NC}"
    echo "1. Update your client applications to use the new function URL"
    echo "2. Monitor function logs: gcloud functions logs read $FUNCTION_NAME"
    echo "3. Check function metrics in the GCP Console"
}

# First-time deployment (skips migration)
main_first_time() {
    echo -e "${GREEN}🚀 Starting first-time Cloud Function deployment...${NC}"
    echo -e "${YELLOW}ℹ️  Skipping database migration (tables will be created by Terraform)${NC}"

    check_env_vars
    validate_environment
    deploy_with_terraform
    test_function

    echo -e "${GREEN}🎉 First-time deployment completed successfully!${NC}"
    echo -e "${YELLOW}📝 Next steps:${NC}"
    echo "1. Update your client applications to use the new function URL"
    echo "2. Monitor function logs: gcloud functions logs read $FUNCTION_NAME"
    echo "3. Check function metrics in the GCP Console"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "first-time")
        main_first_time
        ;;
    "package")
        package_function
        ;;
    "test")
        test_function
        ;;
    "cleanup")
        cleanup
        ;;
    *)
        echo "Usage: $0 [deploy|first-time|package|test|cleanup]"
        echo "  deploy     - Full deployment with migration (default)"
        echo "  first-time - First-time deployment (skips migration)"
        echo "  package    - Package function only"
        echo "  test       - Test deployed function"
        echo "  cleanup    - Clean up temporary files"
        exit 1
        ;;
esac
