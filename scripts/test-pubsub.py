#!/usr/bin/env python3
"""
Simple Pub/Sub Emulator Test Script

This script tests the Pub/Sub emulator connectivity and creates basic topics.
"""

import os
import sys

# Set emulator environment
os.environ["PUBSUB_EMULATOR_HOST"] = "localhost:8085"
os.environ["GCP_PROJECT_ID"] = "local-project"

try:
    from google.api_core import exceptions as gcp_exceptions
    from google.cloud import pubsub_v1

    print("🧪 Testing Pub/Sub emulator connectivity...")

    project_id = "local-project"
    publisher = pubsub_v1.PublisherClient()
    subscriber = pubsub_v1.SubscriberClient()

    # Test basic connectivity
    project_path = f"projects/{project_id}"

    try:
        topics = list(publisher.list_topics(request={"project": project_path}))
        print(f"✅ Pub/Sub emulator is working! Found {len(topics)} topics.")

        if topics:
            for topic in topics:
                topic_name = topic.name.split("/")[-1]
                print(f"  📋 Topic: {topic_name}")
        else:
            print("  ℹ️  No topics found (this is normal for a fresh emulator)")

        # Create a test topic
        test_topic_name = "test-connectivity"
        test_topic_path = publisher.topic_path(project_id, test_topic_name)

        try:
            publisher.create_topic(request={"name": test_topic_path})
            print(f"✅ Successfully created test topic: {test_topic_name}")
        except gcp_exceptions.AlreadyExists:
            print(f"ℹ️  Test topic already exists: {test_topic_name}")

        print("🎉 Pub/Sub emulator test completed successfully!")

    except Exception as e:
        print(f"❌ Failed to connect to Pub/Sub emulator: {e}")
        print("   Make sure the emulator is running on localhost:8085")
        sys.exit(1)

except ImportError as e:
    print(f"❌ Missing required package: {e}")
    print("   Please install: pip install google-cloud-pubsub")
    sys.exit(1)
