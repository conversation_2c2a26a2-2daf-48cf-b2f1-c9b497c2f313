#!/bin/bash

# Local Demo Script - Runs API locally instead of Docker
# This avoids Docker dependency issues

set -e

# Colors for output
BLUE='\033[36m'
GREEN='\033[32m'
YELLOW='\033[33m'
RED='\033[31m'
RESET='\033[0m'

echo -e "${BLUE}🎭 VALIDATOR TOKEN GATEWAY - LOCAL DEMO${RESET}"
echo "This demo runs the API locally and validators in the background"
echo ""

# Step 1: Setup environment
echo -e "${BLUE}📋 Step 1: Setting up environment...${RESET}"
export PUBSUB_EMULATOR_HOST=localhost:8085
export GCP_PROJECT_ID=local-project
export ENVIRONMENT=development
export DATABASE_URL=postgresql://postgres:postgres@localhost:5432/validator_gateway
export REDIS_HOST=localhost
export REDIS_PORT=6379
export REDIS_PASSWORD=redispassword
export JWT_SECRET=dev_jwt_secret_for_testing_only

# Step 2: Start only the infrastructure (no API container)
echo -e "${BLUE}📋 Step 2: Starting infrastructure services...${RESET}"

# Stop any existing containers
docker-compose down > /dev/null 2>&1 || true

# Start only the infrastructure services
docker-compose up -d db redis

echo "⏳ Waiting for database and Redis to be ready..."
sleep 10

# Start GCP emulators
docker-compose -f docker-compose.yml -f docker-compose.local-gcp.yml up -d pubsub-emulator secret-manager-mock

echo "⏳ Waiting for emulators to start..."
sleep 15

# Step 3: Initialize database locally
echo -e "${BLUE}📋 Step 3: Initializing database...${RESET}"
uv run python -m init_db

# Step 4: Test Pub/Sub connectivity
echo -e "${BLUE}📋 Step 4: Testing Pub/Sub connectivity...${RESET}"
python3 scripts/test-pubsub.py

# Step 5: Start API locally
echo -e "${BLUE}📋 Step 5: Starting API locally...${RESET}"
echo "Starting FastAPI server on http://localhost:8000"

# Start API in background with environment variables
PUBSUB_EMULATOR_HOST=localhost:8085 \
GCP_PROJECT_ID=local-project \
ENVIRONMENT=development \
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/validator_gateway \
REDIS_HOST=localhost \
REDIS_PORT=6379 \
REDIS_PASSWORD=redispassword \
JWT_SECRET=dev_jwt_secret_for_testing_only \
uv run uvicorn main:app --host 0.0.0.0 --port 8000 &
API_PID=$!

# Wait for API to be ready
echo "⏳ Waiting for API to be ready..."
for i in {1..30}; do
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ API is ready!${RESET}"
        break
    fi
    echo "Waiting for API... ($i/30)"
    sleep 2
done

# Step 6: Test authentication
echo -e "${BLUE}📋 Step 6: Testing authentication...${RESET}"
echo "Testing dev token endpoint..."

# Test dev token endpoint
AUTH_RESPONSE=$(curl -s -X POST http://localhost:8000/auth/dev-token \
  -H "Content-Type: application/json")

echo "Authentication response: $AUTH_RESPONSE"
echo ""

# Step 7: Show available topics
echo -e "${BLUE}📋 Step 7: Showing available topics...${RESET}"
python3 -c "
import os
os.environ['PUBSUB_EMULATOR_HOST'] = 'localhost:8085'
from google.cloud import pubsub_v1

publisher = pubsub_v1.PublisherClient()
project_path = 'projects/local-project'

try:
    topics = list(publisher.list_topics(request={'project': project_path}))
    print(f'📋 Available topics ({len(topics)}):')
    for topic in topics:
        topic_name = topic.name.split('/')[-1]
        print(f'  • {topic_name}')
except Exception as e:
    print(f'Error listing topics: {e}')
"

echo ""
echo -e "${GREEN}🎉 Demo setup complete!${RESET}"
echo ""
echo -e "${BLUE}🔗 Available Services:${RESET}"
echo "  📡 API Server: http://localhost:8000"
echo "  📚 API Docs: http://localhost:8000/docs"
echo "  📨 Pub/Sub Emulator: localhost:8085"
echo "  🗄️  Database: localhost:5432"
echo "  🔴 Redis: localhost:6379"
echo ""
echo -e "${BLUE}🧪 Next Steps:${RESET}"
echo "  1. Open a new terminal and run: make monitor-pubsub"
echo "  2. Open another terminal and run: make run-validator1"
echo "  3. Open another terminal and run: make run-validator2"
echo "  4. Watch the real-time communication!"
echo ""
echo -e "${YELLOW}💡 Tip: Keep this terminal open to maintain the services${RESET}"
echo -e "${YELLOW}    Press Ctrl+C to stop all services${RESET}"

# Cleanup function
cleanup() {
    echo -e "\n🛑 Stopping services..."

    # Kill API process
    if [ ! -z "$API_PID" ]; then
        kill $API_PID 2>/dev/null || true
    fi

    # Stop Docker containers
    docker-compose -f docker-compose.yml -f docker-compose.local-gcp.yml down

    echo "✅ Cleanup complete"
    exit 0
}

# Trap Ctrl+C to cleanup
trap cleanup INT TERM

# Keep script running and show API status
echo ""
echo -e "${BLUE}🔄 Services are running... Press Ctrl+C to stop${RESET}"

while true; do
    sleep 10
    # Check if API is still running
    if ! curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo -e "${RED}⚠️  API seems to be down. Restarting...${RESET}"
        # Restart API with environment variables
        PUBSUB_EMULATOR_HOST=localhost:8085 \
        GCP_PROJECT_ID=local-project \
        ENVIRONMENT=development \
        DATABASE_URL=postgresql://postgres:postgres@localhost:5432/validator_gateway \
        REDIS_HOST=localhost \
        REDIS_PORT=6379 \
        REDIS_PASSWORD=redispassword \
        JWT_SECRET=dev_jwt_secret_for_testing_only \
        uv run uvicorn main:app --host 0.0.0.0 --port 8000 &
        API_PID=$!
    fi
done
