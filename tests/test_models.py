"""
Tests for models.
"""

from datetime import datetime, timezone

from core.models import (
    GcpToken,
    GcpTokenModel,
    Neuron,
    NeuronModel,
    Token,
    TokenModel,
)


class TestPydanticModels:
    """Test cases for Pydantic models."""

    def test_validator_model(self):
        """Test Neuron model creation and validation."""
        validator = Neuron(
            hotkey="5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi",
            uid=0,
            stake=1000.0,
            trust=0.9,
            is_validator=True,
        )

        assert validator.hotkey == "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"
        assert validator.uid == 0
        assert validator.stake == 1000.0
        assert validator.trust == 0.9
        assert validator.is_validator

    def test_token_model(self):
        """Test Token model creation and validation."""
        token = Token(access_token="test_token", token_type="bearer")

        assert token.access_token == "test_token"
        assert token.token_type == "bearer"

    def test_pubsub_token_model(self):
        """Test GcpToken model creation and validation."""
        pubsub_token = GcpToken(
            access_token="pubsub_token",
            expires_in=3600,
            scope="https://www.googleapis.com/auth/pubsub",
        )

        assert pubsub_token.access_token == "pubsub_token"
        assert pubsub_token.expires_in == 3600
        assert pubsub_token.token_type == "Bearer"  # Default value
        assert pubsub_token.scope == "https://www.googleapis.com/auth/pubsub"

    def test_pubsub_token_model_custom_token_type(self):
        """Test GcpToken model with custom token type."""
        pubsub_token = GcpToken(
            access_token="pubsub_token",
            expires_in=3600,
            token_type="Custom",
            scope="https://www.googleapis.com/auth/pubsub",
        )

        assert pubsub_token.token_type == "Custom"


class TestSQLAlchemyModels:
    """Test cases for SQLAlchemy models."""

    def test_validator_model_creation(self):
        """Test NeuronModel creation."""
        validator = NeuronModel(
            hotkey="5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi",
            uid=0,
            stake=1000.0,
            trust=0.9,
            is_validator=True,
        )

        assert validator.hotkey == "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"
        assert validator.uid == 0
        assert validator.stake == 1000.0
        assert validator.trust == 0.9
        assert validator.is_validator
        # Note: Default values and timestamps are set by SQLAlchemy when committed to DB

    def test_token_model_creation(self):
        """Test TokenModel creation."""
        expires_at = datetime.now(tz=timezone.utc)
        token = TokenModel(
            token="test_token",
            hotkey="5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi",
            uid=0,
            expires_at=expires_at,
        )

        assert token.token == "test_token"
        assert (
            token.hotkey == "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"
        )
        assert token.uid == 0
        assert token.expires_at == expires_at
        # Note: created_at is set by SQLAlchemy when committed to DB

    def test_pubsub_token_model_creation(self):
        """Test GcpTokenModel creation."""
        expires_at = datetime.now(tz=timezone.utc)
        pubsub_token = GcpTokenModel(
            hotkey="5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi",
            uid=0,
            expires_at=expires_at,
            scopes="https://www.googleapis.com/auth/pubsub",
            target_service_account="<EMAIL>",
        )

        assert (
            pubsub_token.hotkey
            == "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"
        )
        assert pubsub_token.uid == 0
        assert pubsub_token.expires_at == expires_at
        assert pubsub_token.scopes == "https://www.googleapis.com/auth/pubsub"
        assert pubsub_token.target_service_account == "<EMAIL>"
        # Note: created_at is set by SQLAlchemy when committed to DB

    def test_validator_model_table_name(self):
        """Test NeuronModel table name."""
        assert NeuronModel.__tablename__ == "neurons"

    def test_token_model_table_name(self):
        """Test TokenModel table name."""
        assert TokenModel.__tablename__ == "tokens"

    def test_pubsub_token_model_table_name(self):
        """Test GcpTokenModel table name."""
        assert GcpTokenModel.__tablename__ == "gcp_tokens"
