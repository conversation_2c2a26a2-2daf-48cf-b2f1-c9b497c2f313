#!/usr/bin/env python3
"""
Helper script to test publishing to Pub/Sub using a token.
"""

import argparse
import json
import sys
import time
from pathlib import Path

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

import requests
from google.cloud import pubsub_v1
from google.oauth2.credentials import Credentials


def get_pubsub_token(api_url, token):
    """Get a Pub/Sub token from the API."""
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}

    response = requests.post(f"{api_url}/auth/pubsub-token", headers=headers)
    response.raise_for_status()

    return response.json()


def publish_message(token_info, message_data, topic_type="system_events"):
    """Publish a message to Pub/Sub using the token."""
    # Create credentials from the token
    credentials = Credentials(
        token=token_info["access_token"],
        scopes=["https://www.googleapis.com/auth/pubsub"],
    )

    # Create a publisher client
    publisher = pubsub_v1.PublisherClient(credentials=credentials)

    # Get the topic path for the specified topic type
    topic_paths = token_info["topic_paths"]
    topic_path = topic_paths.get(topic_type)
    if not topic_path:
        raise ValueError(f"Topic type '{topic_type}' not found in available topics: {list(topic_paths.keys())}")

    # Publish the message
    future = publisher.publish(
        topic_path,
        data=json.dumps(message_data).encode("utf-8"),
        timestamp=str(int(time.time())),
    )

    # Wait for the message to be published
    message_id = future.result()

    return message_id


def main():
    parser = argparse.ArgumentParser(description="Test publishing to Pub/Sub")
    parser.add_argument("--api-url", default="http://localhost:8000", help="API URL")
    parser.add_argument("--token", required=True, help="JWT token")
    parser.add_argument(
        "--message", default='{"test": "message"}', help="Message to publish (JSON)"
    )

    args = parser.parse_args()

    # Get a Pub/Sub token
    token_info = get_pubsub_token(args.api_url, args.token)
    print(f"Got Pub/Sub token: {token_info['access_token'][:10]}...")
    print(f"Token expires in: {token_info['expires_in']} seconds")
    print(f"Available topics: {list(token_info['topic_paths'].keys())}")

    # Parse the message
    message_data = json.loads(args.message)

    # Publish the message to system_events topic by default
    message_id = publish_message(token_info, message_data, "system_events")
    print(f"Published message to system_events topic with ID: {message_id}")


if __name__ == "__main__":
    main()
