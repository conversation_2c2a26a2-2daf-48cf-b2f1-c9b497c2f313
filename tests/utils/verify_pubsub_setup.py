#!/usr/bin/env python3
"""
Helper script to verify the Pub/Sub setup.
"""

import argparse
import sys
from pathlib import Path

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

import requests


def verify_pubsub_setup(api_url):
    """Verify the Pub/Sub setup using the API endpoint."""
    response = requests.get(f"{api_url}/system/pubsub-status")
    response.raise_for_status()

    results = response.json()

    print("Pub/Sub Setup Verification:")
    print(f"Overall Status: {results['status'].upper()}")
    print("\nChecks:")
    for check, result in results["checks"].items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {check}: {status}")

    print("\nConfiguration:")
    for key, value in results["config"].items():
        print(f"  {key}: {value}")

    return results["status"] == "ok"


def main():
    parser = argparse.ArgumentParser(description="Verify Pub/Sub setup")
    parser.add_argument("--api-url", default="http://localhost:8000", help="API URL")

    args = parser.parse_args()

    success = verify_pubsub_setup(args.api_url)

    if not success:
        print("\n❌ Pub/Sub setup verification failed!")
        sys.exit(1)
    else:
        print("\n✅ Pub/Sub setup verification successful!")


if __name__ == "__main__":
    main()
