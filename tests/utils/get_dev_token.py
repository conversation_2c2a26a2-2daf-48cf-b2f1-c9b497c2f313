#!/usr/bin/env python3
"""
Helper script to get a development token.
"""

import argparse
import sys
from pathlib import Path

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

import requests


def get_dev_token(api_url):
    """Get a development token from the API."""
    response = requests.post(f"{api_url}/auth/dev-token")
    response.raise_for_status()

    token_info = response.json()

    print("Development Token:")
    print(f"Access Token: {token_info['access_token'][:10]}...")
    print(f"Token Type: {token_info['token_type']}")
    print(f"Expires In: {token_info['expires_in']} seconds")

    return token_info


def get_dev_pubsub_token(api_url):
    """Get a development Pub/Sub token from the API."""
    response = requests.post(f"{api_url}/auth/dev-pubsub-token")
    response.raise_for_status()

    token_info = response.json()

    print("\nDevelopment Pub/Sub Token:")
    print(f"Access Token: {token_info['access_token'][:10]}...")
    print(f"Token Type: {token_info['token_type']}")
    print(f"Expires In: {token_info['expires_in']} seconds")
    print(f"Scope: {token_info['scope']}")
    print(f"Topic Path: {token_info['topic_path']}")

    return token_info


def main():
    parser = argparse.ArgumentParser(description="Get development tokens")
    parser.add_argument("--api-url", default="http://localhost:8000", help="API URL")
    parser.add_argument("--pubsub", action="store_true", help="Get Pub/Sub token only")

    args = parser.parse_args()

    if args.pubsub:
        get_dev_pubsub_token(args.api_url)
    else:
        # Get both tokens
        jwt_token = get_dev_token(args.api_url)
        get_dev_pubsub_token(args.api_url)

        # Print command to use test_pubsub.py
        print("\nTo test publishing with this token:")
        print(
            f"python tests/test_pubsub.py --api-url {args.api_url} --token {jwt_token['access_token']}"
        )


if __name__ == "__main__":
    main()
