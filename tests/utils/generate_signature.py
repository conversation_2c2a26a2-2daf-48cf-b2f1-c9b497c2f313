#!/usr/bin/env python3
"""
Helper script to generate a valid signature for authentication.
"""

import argparse
import hashlib
import sys
from pathlib import Path

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

import bittensor as bt

from core.config import settings


def generate_signature(config):
    """Generate a signature for authentication."""
    # Get the message to sign
    message = settings.auth_message
    print(f"Message to sign: {message}")

    # Hash the message
    data_hash = hashlib.sha256(message.encode()).digest()
    wallet = bt.wallet(config=config)
    # Sign the hash
    signature = wallet.hotkey.sign(data_hash).hex()
    ss58_address = wallet.hotkey.ss58_address
    # Print the curl command
    print("\nCURL command:")
    print(f"""curl -X POST "http://localhost:8000/auth/token" \\
-H "Authorization: Bittensor {ss58_address}:{signature}" \\
-H "Content-Type: application/json" \\
| jq""")
    print(f"""curl -X GET "http://localhost:8000/validators/me" \\
-H "Authorization: Bittensor {ss58_address}:{signature}" \\
-H "Content-Type: application/json" \\
| jq""")


def main():
    parser = argparse.ArgumentParser(
        description="Generate a signature for authentication"
    )
    parser.add_argument("--netuid", default=15, type=int, help="netuid")

    bt.Wallet.add_args(parser)
    bt.Subtensor.add_args(parser)
    bt.Axon.add_args(parser)
    bt.logging.add_args(parser)
    bt.PriorityThreadPoolExecutor.add_args(parser)
    config = bt.config(parser)

    generate_signature(config=config)


if __name__ == "__main__":
    main()
