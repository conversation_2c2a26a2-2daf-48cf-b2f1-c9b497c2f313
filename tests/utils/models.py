"""
Simplified models for testing.
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Float, Foreign<PERSON>ey, Integer, String
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class NeuronModel(Base):
    """Neuron model for testing."""

    __tablename__ = "neurons"

    hotkey = Column(String, primary_key=True)
    uid = Column(Integer)
    stake = Column(Float)
    trust = Column(Float)
    is_validator = Column(Boolean)


class TokenModel(Base):
    """Token model for testing."""

    __tablename__ = "tokens"

    id = Column(Integer, primary_key=True, autoincrement=True)
    hotkey = Column(String, ForeignKey("neurons.hotkey"))
    token = Column(String)
