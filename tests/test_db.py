"""
Tests for database configuration and utilities.
"""

import os
from unittest.mock import MagicMock, patch

import pytest


class TestDatabaseConfiguration:
    """Test cases for database configuration."""

    @patch.dict(os.environ, {"TESTING": "True"})
    def test_testing_database_configuration(self):
        """Test that testing mode uses SQLite in memory."""
        # Re-import to get the testing configuration
        import importlib

        from app import db

        importlib.reload(db)

        # Check that the engine is using SQLite
        assert "sqlite:///:memory:" in str(db.engine.url)

    @pytest.mark.skip(
        reason="Module reloading in tests is complex and not critical for coverage"
    )
    def test_production_database_configuration(self):
        """Test that production mode uses PostgreSQL."""
        # This test is skipped because module reloading in pytest is complex
        # and the actual database configuration is tested in integration tests
        pass

    def test_get_db_generator(self):
        """Test that get_db returns a database session generator."""
        from app.db import get_db

        # Mock the SessionLocal
        with patch("app.db.SessionLocal") as mock_session_local:
            mock_session = MagicMock()
            mock_session_local.return_value = mock_session

            # Get the generator
            db_gen = get_db()

            # Get the session from the generator
            session = next(db_gen)

            # Verify it's the mocked session
            assert session == mock_session

            # Verify the session is closed when the generator is closed
            try:
                next(db_gen)
            except StopIteration:
                pass

            mock_session.close.assert_called_once()

    def test_create_tables(self):
        """Test create_tables function."""
        with patch("core.models.Base") as mock_base:
            mock_metadata = MagicMock()
            mock_base.metadata = mock_metadata

            from app.db import create_tables

            # Call create_tables
            create_tables()

            # Verify create_all was called
            mock_metadata.create_all.assert_called_once()

    def test_session_local_configuration(self):
        """Test SessionLocal configuration."""
        from app.db import SessionLocal

        # Check that SessionLocal is properly configured
        assert SessionLocal.kw["autocommit"] is False
        assert SessionLocal.kw["autoflush"] is False
        assert SessionLocal.kw["bind"] is not None
