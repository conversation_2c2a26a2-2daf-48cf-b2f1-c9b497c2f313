"""
Pytest configuration file with shared fixtures.
"""

import os
from unittest.mock import MagicMock, patch

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Set environment variables for testing
os.environ["TESTING"] = "True"
os.environ["DATABASE_URL"] = "sqlite:///:memory:"
os.environ["JWT_SECRET"] = "test_secret"
os.environ["ENVIRONMENT"] = "development"

# Import the app after setting environment variables
from app.db import get_db
from core.models import Base
from main import app

# Create an in-memory SQLite database for testing
engine = create_engine("sqlite:///:memory:", connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="function")
def db():
    """Create a fresh database for each test."""
    # Import all models to ensure they're registered with Base
    from core.models import GcpTokenModel, TokenModel  # noqa: F401

    # Create the tables
    Base.metadata.create_all(bind=engine)

    # Create a new session for the test
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        # Drop the tables after the test
        Base.metadata.drop_all(bind=engine)


@pytest.fixture
def client(db):
    """Create a test client for the FastAPI app."""

    # Override the get_db dependency
    def override_get_db():
        try:
            yield db
        finally:
            pass

    app.dependency_overrides[get_db] = override_get_db

    with TestClient(app) as test_client:
        yield test_client

    # Clear the dependency override
    app.dependency_overrides.clear()


@pytest.fixture
def mock_validator():
    """Create a mock validator."""
    validator = MagicMock()
    validator.hotkey = "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"
    validator.uid = 0
    validator.stake = 1000.0
    validator.trust = 0.9
    return validator


@pytest.fixture(autouse=True)
def mock_bittensor_service():
    """Mock the BittensorService."""
    with patch("core.services.bittensor_service.BittensorService") as mock:
        mock_instance = MagicMock()
        mock.return_value = mock_instance
        mock_instance.verify_signature.return_value = True
        yield mock_instance


@pytest.fixture(autouse=True)
def mock_token_service():
    """Mock the TokenService."""
    with patch("core.services.token_service.TokenService") as mock_class:
        # Create a class-level mock for static methods
        mock_class.validate_token.return_value = (
            True,
            {"sub": "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"},
        )

        # Set up verify_token at the class level
        mock_class.verify_token.return_value = (
            "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"
        )

        # Create an instance-level mock
        mock_instance = MagicMock()
        mock_class.return_value = mock_instance

        # Mock instance methods
        mock_instance.verify_token.return_value = (
            "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"
        )

        # Create a mock token
        mock_token = MagicMock()
        mock_token.access_token = "mock_token"
        mock_token.token_type = "bearer"
        mock_token.expires_in = 3600

        mock_class.create_token.return_value = mock_token
        mock_instance.create_token.return_value = mock_token

        yield mock_class



