"""
Tests for GoogleService.
"""

from unittest.mock import MagicMock, patch

import pytest

from core.models import GcpTokenModel
from core.services.google_service import GoogleService


class TestGoogleService:
    """Test cases for GoogleService."""

    def test_cleanup_expired_pubsub_tokens(self, db):
        """Test cleaning up expired Pub/Sub tokens."""
        from datetime import datetime, timezone

        # Create mock expired tokens with proper datetime attributes
        expired_token1 = MagicMock()
        expired_token1.expires_at = datetime(2020, 1, 1, tzinfo=timezone.utc)  # Expired

        expired_token2 = MagicMock()
        expired_token2.expires_at = datetime(2020, 1, 2, tzinfo=timezone.utc)  # Expired

        # Create mock active token
        active_token = MagicMock()
        active_token.expires_at = datetime(2030, 1, 1, tzinfo=timezone.utc)  # Future

        # Mock database query to return all tokens
        db.query.return_value.all.return_value = [expired_token1, expired_token2, active_token]

        # Call the method
        count = GoogleService.cleanup_expired_pubsub_tokens(db)

        # Verify the result
        assert count == 2
        db.delete.assert_any_call(expired_token1)
        db.delete.assert_any_call(expired_token2)
        db.commit.assert_called_once()

    def test_get_pubsub_token_stats(self, db):
        """Test getting Pub/Sub token statistics."""
        from datetime import datetime, timezone

        # Create mock tokens with proper datetime attributes
        active_token1 = MagicMock()
        active_token1.expires_at = datetime(2030, 1, 1, tzinfo=timezone.utc)  # Future

        active_token2 = MagicMock()
        active_token2.expires_at = datetime(2030, 1, 2, tzinfo=timezone.utc)  # Future

        expired_token1 = MagicMock()
        expired_token1.expires_at = datetime(2020, 1, 1, tzinfo=timezone.utc)  # Expired

        expired_token2 = MagicMock()
        expired_token2.expires_at = datetime(2020, 1, 2, tzinfo=timezone.utc)  # Expired

        expired_token3 = MagicMock()
        expired_token3.expires_at = datetime(2020, 1, 3, tzinfo=timezone.utc)  # Expired

        all_tokens = [active_token1, active_token2, expired_token1, expired_token2, expired_token3]

        # Mock database query to return filtered tokens
        mock_query = MagicMock()
        mock_query.filter.return_value.all.return_value = all_tokens
        db.query.return_value = mock_query

        # Call the method
        stats = GoogleService.get_pubsub_token_stats(db, "test_hotkey")

        # Verify the result
        assert stats["active_tokens"] == 2
        assert stats["total_tokens_issued"] == 5
        assert "last_token_issued" in stats



    def test_generate_pubsub_token_validator_not_found(self, db):
        """Test generating Pub/Sub token when validator not found."""
        # Mock database query to return None
        mock_query = MagicMock()
        mock_query.filter.return_value.first.return_value = None
        db.query.return_value = mock_query

        # Call the method and expect an error
        with pytest.raises(
            ValueError, match="Validator with hotkey test_hotkey not found"
        ):
            GoogleService.generate_pubsub_token("test_hotkey", db)

    @patch("core.services.rate_limit_service.RateLimitService.check_rate_limit")
    def test_generate_pubsub_token_rate_limit_exceeded(self, mock_rate_limit, db):
        """Test generating Pub/Sub token when rate limit exceeded."""
        # Mock validator exists
        mock_validator = MagicMock()
        mock_validator.hotkey = "test_hotkey"
        mock_validator.uid = 0

        # Mock database query
        mock_query = MagicMock()
        mock_query.filter.return_value.first.return_value = mock_validator
        db.query.return_value = mock_query

        # Mock rate limit check to return False (rate limit exceeded)
        mock_rate_limit.return_value = False

        # Call the method and expect an error
        with pytest.raises(ValueError, match="Rate limit exceeded"):
            GoogleService.generate_pubsub_token("test_hotkey", db, rate_limit_check=True)

    def test_generate_pubsub_token_missing_libraries(self, db):
        """Test generating Pub/Sub token when Google libraries are missing."""
        # Mock validator exists
        mock_validator = MagicMock()
        mock_validator.hotkey = "test_hotkey"
        mock_validator.uid = 0

        # Mock database query
        mock_query = MagicMock()
        mock_query.filter.return_value.first.return_value = mock_validator
        db.query.return_value = mock_query

        # Mock missing Google libraries
        with patch("core.services.google_service.impersonated_credentials", None):
            with pytest.raises(RuntimeError, match="Google Cloud libraries not available"):
                GoogleService.generate_pubsub_token("test_hotkey", db, rate_limit_check=False)

    @pytest.fixture
    def db(self):
        """Mock database session."""
        return MagicMock()
