"""
Tests for NeuronService.
"""

from datetime import datetime, timezone
from unittest.mock import MagicMock, patch

import pytest

from core.models import Neuron, NeuronModel
from core.services.neuron_service import NeuronService


class TestNeuronService:
    """Test cases for NeuronService."""

    def test_get_or_create_neuron_new(self, db):
        """Test creating a new validator."""
        # Mock database query to return no existing validator
        mock_query = MagicMock()
        mock_query.filter.return_value.first.return_value = None
        db.query.return_value = mock_query

        # Test data
        hotkey = "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"
        uid = 42
        stake = 1500.0
        trust = 0.95
        is_validator = True

        # Call the method
        result = NeuronService.get_or_create_neuron(db, hotkey, uid, stake, trust, is_validator)

        # Verify database operations
        db.add.assert_called_once()
        db.commit.assert_called_once()
        db.refresh.assert_called_once()

        # Verify the add call was made with a NeuronModel
        add_call_args = db.add.call_args[0][0]
        assert isinstance(add_call_args, NeuronModel)
        assert add_call_args.hotkey == hotkey
        assert add_call_args.uid == uid
        assert add_call_args.stake == stake
        assert add_call_args.trust == trust

    def test_get_or_create_neuron_existing(self, db):
        """Test updating an existing validator."""
        # Mock existing validator
        existing_validator = NeuronModel(
            hotkey="5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi",
            uid=10,
            stake=500.0,
            trust=0.5,
        )

        mock_query = MagicMock()
        mock_query.filter.return_value.first.return_value = existing_validator
        db.query.return_value = mock_query

        # Test data (updated values)
        hotkey = "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"
        uid = 42
        stake = 1500.0
        trust = 0.95
        is_validator = True

        # Call the method
        with patch("core.services.neuron_service.datetime") as mock_datetime:
            mock_now = datetime.now(tz=timezone.utc)
            mock_datetime.now.return_value = mock_now

            result = NeuronService.get_or_create_neuron(
                db, hotkey, uid, stake, trust, is_validator
            )

        # Verify the existing validator was updated
        assert existing_validator.uid == uid
        assert existing_validator.stake == stake
        assert existing_validator.trust == trust
        assert existing_validator.last_active == mock_now

        # Verify database operations
        db.add.assert_not_called()  # Should not add new record
        db.commit.assert_called_once()
        db.refresh.assert_not_called()  # Should not refresh for existing

        # Verify the result is the existing validator
        assert result == existing_validator

    def test_to_pydantic(self):
        """Test converting SQLAlchemy model to Pydantic model."""
        # Create a NeuronModel
        validator_db = NeuronModel(
            hotkey="5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi",
            uid=42,
            stake=1500.0,
            trust=0.95,
            is_validator=True,
        )

        # Call the method
        result = NeuronService.to_pydantic(validator_db)

        # Verify the result
        assert isinstance(result, Neuron)
        assert result.hotkey == validator_db.hotkey
        assert result.uid == validator_db.uid
        assert result.stake == validator_db.stake
        assert result.trust == validator_db.trust

    def test_get_or_create_neuron_database_filter(self, db):
        """Test that the correct database filter is applied."""
        # Mock database query
        mock_query = MagicMock()
        mock_filter = MagicMock()
        mock_query.filter.return_value = mock_filter
        mock_filter.first.return_value = None
        db.query.return_value = mock_query

        # Test data
        hotkey = "test_hotkey"
        uid = 1
        stake = 100.0
        trust = 0.8
        is_validator = True

        # Call the method
        NeuronService.get_or_create_neuron(db, hotkey, uid, stake, trust, is_validator)

        # Verify the query was made for NeuronModel
        db.query.assert_called_once_with(NeuronModel)

        # Verify the filter was applied (we can't easily test the exact filter condition
        # due to SQLAlchemy's filter syntax, but we can verify filter was called)
        mock_query.filter.assert_called_once()

    @pytest.fixture
    def db(self):
        """Mock database session."""
        return MagicMock()
