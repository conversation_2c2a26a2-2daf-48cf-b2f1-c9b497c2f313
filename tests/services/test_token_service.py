"""
Comprehensive tests for TokenService.
"""

from datetime import datetime, timedelta, timezone
from unittest.mock import MagicMock, patch

import jwt
import pytest

from core.models import Neuron, Token
from core.services.token_service import TokenService


class TestTokenService:
    """Test cases for TokenService."""

    def test_create_token_new_validator(self, db):
        """Test creating token for new validator."""
        # Create a validator
        validator = Neuron(
            hotkey="5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi",
            uid=0,
            stake=1000.0,
            trust=0.9,
            is_validator=True,
        )

        # Mock the database query to return no existing token
        mock_query = MagicMock()
        mock_query.filter.return_value.first.return_value = None
        db.query.return_value = mock_query

        # Call the method
        with patch("core.services.token_service.jwt.encode") as mock_encode:
            mock_encode.return_value = "mock_jwt_token"

            result = TokenService.create_token(validator, db)

        # Verify the result
        assert isinstance(result, Token)
        assert result.access_token == "mock_jwt_token"
        assert result.token_type == "bearer"

        # Verify database operations
        db.add.assert_called_once()
        db.commit.assert_called_once()

    def test_create_token_existing_validator(self, db):
        """Test creating token for validator with existing token."""
        # Create a validator
        validator = Neuron(
            hotkey="5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi",
            uid=0,
            stake=1000.0,
            trust=0.9,
            is_validator=True,
        )

        # Mock existing token
        existing_token = MagicMock()
        mock_query = MagicMock()
        mock_query.filter.return_value.first.return_value = existing_token
        db.query.return_value = mock_query

        # Call the method
        with patch("core.services.token_service.jwt.encode") as mock_encode:
            mock_encode.return_value = "new_jwt_token"

            result = TokenService.create_token(validator, db)

        # Verify the result
        assert isinstance(result, Token)
        assert result.access_token == "new_jwt_token"
        assert result.token_type == "bearer"

        # Verify existing token was deleted
        db.delete.assert_called_once_with(existing_token)

        # Verify database operations (delete commit + add commit)
        assert db.commit.call_count == 2
        db.add.assert_called_once()

    @patch("core.services.token_service.settings")
    def test_create_token_payload_content(self, mock_settings, db):
        """Test that token payload contains correct information."""
        # Mock settings
        mock_settings.JWT_SECRET = "test_secret"
        mock_settings.TOKEN_EXPIRY = 3600

        # Create a validator
        validator = Neuron(
            hotkey="5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi",
            uid=42,
            stake=1500.0,
            trust=0.95,
            is_validator=True,
        )

        # Mock the database query to return no existing token
        mock_query = MagicMock()
        mock_query.filter.return_value.first.return_value = None
        db.query.return_value = mock_query

        # Call the method
        with (
            patch("core.services.token_service.jwt.encode") as mock_encode,
            patch("core.services.token_service.time.time") as mock_time,
        ):
            mock_time.return_value = 1000000
            mock_encode.return_value = "test_token"

            TokenService.create_token(validator, db)

            # Verify the payload passed to jwt.encode
            call_args = mock_encode.call_args
            payload = call_args[0][0]

            assert payload["sub"] == validator.hotkey
            assert payload["uid"] == validator.uid
            assert payload["stake"] == validator.stake
            assert payload["trust"] == validator.trust
            assert payload["exp"] == 1000000 + 3600
            assert payload["iat"] == 1000000

    def test_validate_token_valid(self, db):
        """Test validating a valid token."""
        token = "valid_token"

        # Mock token in database
        mock_token_db = MagicMock()
        mock_token_db.expires_at = datetime.now(tz=timezone.utc) + timedelta(hours=1)

        mock_query = MagicMock()
        mock_query.filter.return_value.first.return_value = mock_token_db
        db.query.return_value = mock_query

        # Mock JWT decode
        expected_payload = {"sub": "test_hotkey", "uid": 0}

        with patch("core.services.token_service.jwt.decode") as mock_decode:
            mock_decode.return_value = expected_payload

            is_valid, payload = TokenService.validate_token(token, db)

        # Verify the result
        assert is_valid is True
        assert payload == expected_payload

    def test_validate_token_not_in_database(self, db):
        """Test validating token not in database."""
        token = "invalid_token"

        # Mock token not found in database
        mock_query = MagicMock()
        mock_query.filter.return_value.first.return_value = None
        db.query.return_value = mock_query

        is_valid, payload = TokenService.validate_token(token, db)

        # Verify the result
        assert is_valid is False
        assert payload == {}

    def test_validate_token_expired(self, db):
        """Test validating an expired token."""
        token = "expired_token"

        # Mock expired token in database
        mock_token_db = MagicMock()
        mock_token_db.expires_at = datetime.now(tz=timezone.utc) - timedelta(hours=1)

        mock_query = MagicMock()
        mock_query.filter.return_value.first.return_value = mock_token_db
        db.query.return_value = mock_query

        is_valid, payload = TokenService.validate_token(token, db)

        # Verify the result
        assert is_valid is False
        assert payload == {}

    def test_validate_token_jwt_error(self, db):
        """Test validating token with JWT decode error."""
        token = "malformed_token"

        # Mock token in database
        mock_token_db = MagicMock()
        mock_token_db.expires_at = datetime.now(tz=timezone.utc) + timedelta(hours=1)

        mock_query = MagicMock()
        mock_query.filter.return_value.first.return_value = mock_token_db
        db.query.return_value = mock_query

        # Mock JWT decode to raise an error
        with patch("core.services.token_service.jwt.decode") as mock_decode:
            mock_decode.side_effect = jwt.PyJWTError("Invalid token")

            is_valid, payload = TokenService.validate_token(token, db)

        # Verify the result
        assert is_valid is False
        assert payload == {}

    @pytest.fixture
    def db(self):
        """Mock database session."""
        return MagicMock()
