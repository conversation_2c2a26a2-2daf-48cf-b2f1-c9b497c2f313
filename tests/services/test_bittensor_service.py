"""
Tests for BittensorService.
"""

from unittest.mock import MagicMock, patch

from core.services.bittensor_service import BittensorService


class TestBittensorService:
    """Test cases for BittensorService."""

    @patch("core.services.bittensor_service.bittensor")
    def test_get_components(self, mock_bittensor):
        """Test getting Bittensor components."""
        # Mock the bittensor components
        mock_subtensor = MagicMock()
        mock_metagraph = MagicMock()

        mock_bittensor.subtensor.return_value = mock_subtensor
        mock_bittensor.metagraph.return_value = mock_metagraph

        # Call the method
        subtensor, metagraph = BittensorService.get_components()

        # Verify the calls
        mock_bittensor.subtensor.assert_called_once()
        mock_bittensor.metagraph.assert_called_once()
        mock_metagraph.sync.assert_called_once()

        # Verify the return values
        assert subtensor == mock_subtensor
        assert metagraph == mock_metagraph

    @patch("core.services.bittensor_service.bittensor")
    def test_verify_signature_success(self, mock_bittensor):
        """Test successful signature verification."""
        # Mock keypair
        mock_keypair = MagicMock()
        mock_keypair.verify.return_value = True
        mock_bittensor.Keypair.return_value = mock_keypair

        # Test data
        signature = "abcdef123456"
        message = "test message"
        ss58_address = "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"

        # Call the method
        result = BittensorService.verify_signature(signature, message, ss58_address)

        # Verify the result
        assert result is True

        # Verify the keypair was created with the correct address
        mock_bittensor.Keypair.assert_called_once_with(ss58_address=ss58_address)

        # Verify the signature verification was called with direct message first
        expected_message_bytes = message.encode()
        expected_signature_bytes = bytes.fromhex(signature)
        mock_keypair.verify.assert_called_once_with(
            expected_message_bytes, expected_signature_bytes
        )

    @patch("core.services.bittensor_service.bittensor")
    def test_verify_signature_failure(self, mock_bittensor):
        """Test failed signature verification."""
        # Mock keypair
        mock_keypair = MagicMock()
        mock_keypair.verify.return_value = False
        mock_bittensor.Keypair.return_value = mock_keypair

        # Test data
        signature = "invalid_signature"
        message = "test message"
        ss58_address = "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"

        # Call the method
        result = BittensorService.verify_signature(signature, message, ss58_address)

        # Verify the result
        assert result is False

    @patch("core.services.bittensor_service.bittensor")
    def test_verify_signature_exception(self, mock_bittensor):
        """Test signature verification with exception."""
        # Mock keypair to raise an exception
        mock_bittensor.Keypair.side_effect = Exception("Invalid keypair")

        # Test data
        signature = "abcdef123456"
        message = "test message"
        ss58_address = "invalid_address"

        # Call the method
        result = BittensorService.verify_signature(signature, message, ss58_address)

        # Verify the result
        assert result is False

    def test_is_validator_found_with_stake(self):
        """Test is_validator when hotkey is found and has stake."""
        # Mock metagraph
        mock_metagraph = MagicMock()
        hotkey = "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"

        # Set up the mock
        mock_metagraph.hotkeys = [hotkey, "other_hotkey"]
        mock_metagraph.S = [1000.0, 500.0]  # Stakes
        mock_metagraph.T = [0.9, 0.8]  # Trust scores
        mock_metagraph.weights = {
            0: MagicMock(
                sum=MagicMock(return_value=MagicMock(
                item=MagicMock(return_value=1))))}
        # Call the method
        is_validator, uid, stake, trust = BittensorService.is_validator(
            mock_metagraph, hotkey
        )

        # Verify the results
        assert is_validator is True
        assert uid == 0
        assert stake == 1000.0
        assert trust == 0.9

    def test_is_validator_found_no_stake(self):
        """Test is_validator when hotkey is found but has no stake."""
        # Mock metagraph
        mock_metagraph = MagicMock()
        hotkey = "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"

        # Set up the mock
        mock_metagraph.hotkeys = [hotkey, "other_hotkey"]
        mock_metagraph.S = [0.0, 500.0]  # No stake for first hotkey
        mock_metagraph.T = [0.9, 0.8]  # Trust scores

        # Call the method
        is_validator, uid, stake, trust = BittensorService.is_validator(
            mock_metagraph, hotkey
        )

        # Verify the results
        assert is_validator is False
        assert uid == 0
        assert stake == 0.0
        assert trust == 0.9

    def test_is_validator_not_found(self):
        """Test is_validator when hotkey is not found."""
        # Mock metagraph
        mock_metagraph = MagicMock()
        hotkey = "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"

        # Set up the mock - hotkey not in list
        mock_metagraph.hotkeys = ["other_hotkey1", "other_hotkey2"]

        # Call the method
        is_validator, uid, stake, trust = BittensorService.is_validator(
            mock_metagraph, hotkey
        )

        # Verify the results
        assert is_validator is False
        assert uid == -1
        assert stake == 0.0
        assert trust == 0.0
