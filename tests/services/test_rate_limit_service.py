"""Tests for RateLimitService."""

import pytest
from datetime import datetime, timedelta, timezone
from unittest.mock import MagicMock, patch

from core.models import RateLimitModel
from core.services.rate_limit_service import RateLimitService


class TestRateLimitService:
    """Test cases for RateLimitService."""

    def test_check_rate_limit_new_client(self, db):
        """Test rate limit check for a new client."""
        # Mock database query to return None (no existing record)
        mock_query = MagicMock()
        mock_query.filter.return_value.order_by.return_value.first.return_value = None
        db.query.return_value = mock_query

        # Call the method
        result = RateLimitService.check_rate_limit(db, "192.168.1.1", 10)

        # Verify the result
        assert result is True
        db.add.assert_called_once()
        db.commit.assert_called_once()

    def test_check_rate_limit_within_limit_same_window(self, db):
        """Test rate limit check within limit in the same window."""
        # Create mock existing rate limit record
        mock_rate_limit = MagicMock()
        mock_rate_limit.request_count = 5
        mock_rate_limit.window_start = datetime.now(tz=timezone.utc) - timedelta(seconds=30)

        # Mock database query
        mock_query = MagicMock()
        mock_query.filter.return_value.order_by.return_value.first.return_value = mock_rate_limit
        db.query.return_value = mock_query

        # Call the method
        result = RateLimitService.check_rate_limit(db, "192.168.1.1", 10)

        # Verify the result
        assert result is True
        assert mock_rate_limit.request_count == 6
        db.commit.assert_called_once()

    def test_check_rate_limit_exceeds_limit_same_window(self, db):
        """Test rate limit check when limit is exceeded in the same window."""
        # Create mock existing rate limit record at the limit
        mock_rate_limit = MagicMock()
        mock_rate_limit.request_count = 10
        mock_rate_limit.window_start = datetime.now(tz=timezone.utc) - timedelta(seconds=30)

        # Mock database query
        mock_query = MagicMock()
        mock_query.filter.return_value.order_by.return_value.first.return_value = mock_rate_limit
        db.query.return_value = mock_query

        # Call the method
        result = RateLimitService.check_rate_limit(db, "192.168.1.1", 10)

        # Verify the result
        assert result is False
        # Should not increment or commit when limit exceeded
        assert mock_rate_limit.request_count == 10
        db.commit.assert_not_called()

    def test_check_rate_limit_new_window(self, db):
        """Test rate limit check in a new window (resets counter)."""
        # Create mock existing rate limit record from old window
        mock_rate_limit = MagicMock()
        mock_rate_limit.request_count = 10
        mock_rate_limit.window_start = datetime.now(tz=timezone.utc) - timedelta(minutes=5)

        # Mock database query
        mock_query = MagicMock()
        mock_query.filter.return_value.order_by.return_value.first.return_value = mock_rate_limit
        db.query.return_value = mock_query

        # Call the method
        result = RateLimitService.check_rate_limit(db, "192.168.1.1", 10)

        # Verify the result
        assert result is True
        assert mock_rate_limit.request_count == 1  # Reset to 1
        db.commit.assert_called_once()

    def test_check_rate_limit_with_custom_environment(self, db):
        """Test rate limit check with custom environment."""
        # Mock database query to return None
        mock_query = MagicMock()
        mock_query.filter.return_value.order_by.return_value.first.return_value = None
        db.query.return_value = mock_query

        # Call the method with custom environment
        result = RateLimitService.check_rate_limit(
            db, "192.168.1.1", 10, environment="test"
        )

        # Verify the result
        assert result is True
        db.add.assert_called_once()

        # Verify the rate limit record was created with correct environment
        added_record = db.add.call_args[0][0]
        assert hasattr(added_record, 'environment')

    def test_check_rate_limit_with_custom_endpoint(self, db):
        """Test rate limit check with custom endpoint."""
        # Mock database query to return None
        mock_query = MagicMock()
        mock_query.filter.return_value.order_by.return_value.first.return_value = None
        db.query.return_value = mock_query

        # Call the method with custom endpoint
        result = RateLimitService.check_rate_limit(
            db, "192.168.1.1", 10, endpoint="auth"
        )

        # Verify the result
        assert result is True
        db.add.assert_called_once()

    def test_check_rate_limit_timezone_naive_datetime_new_window(self, db):
        """Test rate limit check with timezone-naive datetime from database (new window scenario)."""
        # Create mock existing rate limit record with naive datetime from old window
        mock_rate_limit = MagicMock()
        mock_rate_limit.request_count = 10
        # Use an old naive datetime that will be treated as a new window
        mock_rate_limit.window_start = datetime.now() - timedelta(minutes=5)  # Naive datetime

        # Mock database query
        mock_query = MagicMock()
        mock_query.filter.return_value.order_by.return_value.first.return_value = mock_rate_limit
        db.query.return_value = mock_query

        # Call the method
        result = RateLimitService.check_rate_limit(db, "192.168.1.1", 10)

        # Verify the result - should reset to new window
        assert result is True
        assert mock_rate_limit.request_count == 1  # Reset to 1 for new window

    def test_cleanup_old_rate_limits(self, db):
        """Test cleaning up old rate limit records."""
        # Create mock old and new records
        old_record1 = MagicMock()
        old_record1.created_at = datetime(2020, 1, 1, tzinfo=timezone.utc)

        old_record2 = MagicMock()
        old_record2.created_at = datetime(2020, 1, 2, tzinfo=timezone.utc)

        new_record = MagicMock()
        new_record.created_at = datetime.now(tz=timezone.utc) - timedelta(hours=1)

        all_records = [old_record1, old_record2, new_record]

        # Mock database query
        db.query.return_value.all.return_value = all_records

        # Call the method
        count = RateLimitService.cleanup_old_rate_limits(db, hours_old=24)

        # Verify the result
        assert count == 2
        db.delete.assert_any_call(old_record1)
        db.delete.assert_any_call(old_record2)
        db.commit.assert_called_once()

    def test_cleanup_old_rate_limits_timezone_naive(self, db):
        """Test cleaning up old rate limit records with naive datetimes."""
        # Create mock old record with naive datetime
        old_record = MagicMock()
        old_record.created_at = datetime(2020, 1, 1)  # Naive datetime

        new_record = MagicMock()
        new_record.created_at = datetime.now() - timedelta(hours=1)  # Naive datetime

        all_records = [old_record, new_record]

        # Mock database query
        db.query.return_value.all.return_value = all_records

        # Call the method
        count = RateLimitService.cleanup_old_rate_limits(db, hours_old=24)

        # Verify the result
        assert count == 1
        db.delete.assert_called_once_with(old_record)

    def test_get_rate_limit_status_no_existing_record(self, db):
        """Test getting rate limit status when no record exists."""
        # Mock database query to return None
        mock_query = MagicMock()
        mock_query.filter.return_value.order_by.return_value.first.return_value = None
        db.query.return_value = mock_query

        # Call the method
        status = RateLimitService.get_rate_limit_status(db, "192.168.1.1")

        # Verify the result
        assert status["requests_made"] == 0
        assert "window_start" in status
        assert "window_end" in status
        assert "environment" in status

    def test_get_rate_limit_status_existing_record(self, db):
        """Test getting rate limit status when record exists."""
        # Create mock existing rate limit record
        mock_rate_limit = MagicMock()
        mock_rate_limit.request_count = 7
        mock_rate_limit.window_start = datetime.now(tz=timezone.utc) - timedelta(minutes=1)

        # Mock database query
        mock_query = MagicMock()
        mock_query.filter.return_value.order_by.return_value.first.return_value = mock_rate_limit
        db.query.return_value = mock_query

        # Call the method
        status = RateLimitService.get_rate_limit_status(db, "192.168.1.1")

        # Verify the result
        assert status["requests_made"] == 7
        assert "window_start" in status
        assert "window_end" in status
        assert "environment" in status

    @patch("core.services.rate_limit_service.settings")
    def test_check_rate_limit_uses_default_environment(self, mock_settings, db):
        """Test that check_rate_limit uses default environment from settings."""
        mock_settings.ENVIRONMENT = "production"

        # Mock database query to return None
        mock_query = MagicMock()
        mock_query.filter.return_value.order_by.return_value.first.return_value = None
        db.query.return_value = mock_query

        # Call the method without specifying environment
        result = RateLimitService.check_rate_limit(db, "192.168.1.1", 10)

        # Verify the result
        assert result is True
        db.add.assert_called_once()

    @patch("core.services.rate_limit_service.settings")
    def test_get_rate_limit_status_uses_default_environment(self, mock_settings, db):
        """Test that get_rate_limit_status uses default environment from settings."""
        mock_settings.ENVIRONMENT = "development"

        # Mock database query to return None
        mock_query = MagicMock()
        mock_query.filter.return_value.order_by.return_value.first.return_value = None
        db.query.return_value = mock_query

        # Call the method without specifying environment
        status = RateLimitService.get_rate_limit_status(db, "192.168.1.1")

        # Verify the result
        assert status["environment"] == "development"

    @pytest.fixture
    def db(self):
        """Mock database session."""
        return MagicMock()
