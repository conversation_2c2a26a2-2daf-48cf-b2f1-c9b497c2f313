"""
Tests for auth module functions.
"""

from unittest.mock import MagicMock, patch

import pytest
from fastapi import <PERSON><PERSON><PERSON><PERSON>xception

from app.auth import get_current_validator
from core.models import Neuron


class TestAuthModule:
    """Test cases for auth module functions."""

    @patch("app.auth.BittensorService")
    @patch("app.auth.NeuronService")
    @pytest.mark.asyncio
    async def test_get_current_validator_success(
        self, mock_validator_service, mock_bittensor_service
    ):
        """Test successful validator authentication."""
        # Mock the services
        mock_subtensor = MagicMock()
        mock_metagraph = MagicMock()
        mock_bittensor_service.get_components.return_value = (
            mock_subtensor,
            mock_metagraph,
        )
        mock_bittensor_service.is_validator.return_value = (True, 0, 1000.0, 0.9)
        mock_bittensor_service.verify_signature.return_value = True

        mock_validator_db = MagicMock()
        mock_validator_service.get_or_create_neuron.return_value = mock_validator_db

        mock_validator = Neuron(
            hotkey="5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi",
            uid=0,
            stake=1000.0,
            trust=0.9,
            is_validator=True
        )
        mock_validator_service.to_pydantic.return_value = mock_validator

        # Mock database session
        mock_db = MagicMock()

        # Test the function
        authorization = (
            "Bittensor 5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi:signature123"
        )

        result = await get_current_validator(mock_db, authorization)

        # Verify the result
        assert result == mock_validator

        # Verify the service calls
        mock_bittensor_service.get_components.assert_called_once()
        mock_bittensor_service.is_validator.assert_called_once()
        mock_bittensor_service.verify_signature.assert_called_once()
        mock_validator_service.get_or_create_neuron.assert_called_once()
        mock_validator_service.to_pydantic.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_current_validator_invalid_format(self):
        """Test validator authentication with invalid authorization format."""
        mock_db = MagicMock()

        # Test with invalid format
        authorization = "Bearer token123"

        with pytest.raises(HTTPException) as exc_info:
            await get_current_validator(mock_db, authorization)

        assert exc_info.value.status_code == 401
        assert "Invalid authorization format" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    async def test_get_current_validator_invalid_credential_format(self):
        """Test validator authentication with invalid credential format."""
        mock_db = MagicMock()

        # Test with invalid credential format (missing colon)
        authorization = "Bittensor 5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"

        with pytest.raises(HTTPException) as exc_info:
            await get_current_validator(mock_db, authorization)

        assert exc_info.value.status_code == 401
        assert "Invalid credential format" in str(exc_info.value.detail)

    @patch("app.auth.BittensorService")
    @pytest.mark.asyncio
    async def test_get_current_validator_not_validator(self, mock_bittensor_service):
        """Test authentication when hotkey is not a validator."""
        # Mock the services
        mock_subtensor = MagicMock()
        mock_metagraph = MagicMock()
        mock_bittensor_service.get_components.return_value = (
            mock_subtensor,
            mock_metagraph,
        )
        mock_bittensor_service.is_validator.return_value = (False, -1, 0.0, 0.0)

        mock_db = MagicMock()

        # Test the function
        authorization = (
            "Bittensor 5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi:signature123"
        )

        with pytest.raises(HTTPException) as exc_info:
            await get_current_validator(mock_db, authorization)

        assert exc_info.value.status_code == 403
        assert "Not a validator hotkey" in str(exc_info.value.detail)

    @patch("app.auth.BittensorService")
    @pytest.mark.asyncio
    async def test_get_current_validator_invalid_signature(
        self, mock_bittensor_service
    ):
        """Test authentication with invalid signature."""
        # Mock the services
        mock_subtensor = MagicMock()
        mock_metagraph = MagicMock()
        mock_bittensor_service.get_components.return_value = (
            mock_subtensor,
            mock_metagraph,
        )
        mock_bittensor_service.is_validator.return_value = (True, 0, 1000.0, 0.9)
        mock_bittensor_service.verify_signature.return_value = (
            False  # Invalid signature
        )

        mock_db = MagicMock()

        # Test the function
        authorization = "Bittensor 5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi:invalid_signature"

        with pytest.raises(HTTPException) as exc_info:
            await get_current_validator(mock_db, authorization)

        assert exc_info.value.status_code == 401
        assert "Invalid signature" in str(exc_info.value.detail)
