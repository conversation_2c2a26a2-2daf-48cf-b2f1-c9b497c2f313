"""
Tests for authentication routes.
"""

from unittest.mock import patch

from core.models import Neuron


class TestAuthRoutes:
    """Test cases for authentication routes."""

    @patch("app.routes.auth.RateLimitService.check_rate_limit")
    @patch("app.routes.auth.GoogleService.generate_pubsub_token")
    @patch("app.routes.auth.GoogleService.get_pubsub_topic_paths")
    @patch("app.routes.auth.time.time")
    def test_get_pubsub_token_success(
        self,
        mock_time,
        mock_get_topic_paths,
        mock_generate_token,
        mock_check_rate_limit,
        client,
    ):
        """Test successful Pub/Sub token generation."""
        # Mock dependencies
        mock_time.return_value = 1000000
        mock_check_rate_limit.return_value = True
        mock_generate_token.return_value = ("mock_pubsub_token", 1003600)
        mock_get_topic_paths.return_value = {
            "allocation_events": "projects/test/topics/allocation-events",
            "miner_events": "projects/test/topics/miner-events",
            "system_events": "projects/test/topics/system-events",
            "validation_events": "projects/test/topics/validation-events",
        }

        # Mock validator
        mock_validator = Neuron(
            hotkey="5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi",
            uid=0,
            stake=1000.0,
            trust=0.9,
            is_validator=True,
        )

        # Override the dependency
        from app.auth import get_neuron_from_token
        from main import app

        def mock_get_neuron_from_token():
            return mock_validator

        app.dependency_overrides[get_neuron_from_token] = (
            mock_get_neuron_from_token
        )

        try:
            # Make the request
            response = client.post(
                "/auth/pubsub-token", headers={"Authorization": "Bearer valid_token"}
            )
        finally:
            # Clean up
            app.dependency_overrides.pop(get_neuron_from_token, None)

        # Verify the response
        assert response.status_code == 200
        data = response.json()
        assert data["access_token"] == "mock_pubsub_token"
        assert data["expires_in"] == 3600
        assert data["token_type"] == "Bearer"
        assert data["scope"] == "https://www.googleapis.com/auth/pubsub https://www.googleapis.com/auth/cloud-platform"
        assert "topic_paths" in data
        assert data["topic_paths"]["allocation_events"] == "projects/test/topics/allocation-events"
        assert data["topic_paths"]["miner_events"] == "projects/test/topics/miner-events"
        assert data["topic_paths"]["system_events"] == "projects/test/topics/system-events"
        assert data["topic_paths"]["validation_events"] == "projects/test/topics/validation-events"

    @patch("app.routes.auth.RateLimitService.check_rate_limit")
    def test_get_pubsub_token_rate_limited(self, mock_check_rate_limit, client):
        """Test Pub/Sub token generation when rate limited."""
        # Mock rate limit exceeded
        mock_check_rate_limit.return_value = False

        # Mock validator
        mock_validator = Neuron(
            hotkey="5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi",
            uid=0,
            stake=1000.0,
            trust=0.9,
            is_validator=True,
        )

        # Override the dependency
        from app.auth import get_neuron_from_token
        from main import app

        def mock_get_neuron_from_token():
            return mock_validator

        app.dependency_overrides[get_neuron_from_token] = (
            mock_get_neuron_from_token
        )

        try:
            # Make the request
            response = client.post(
                "/auth/pubsub-token", headers={"Authorization": "Bearer valid_token"}
            )
        finally:
            # Clean up
            app.dependency_overrides.pop(get_neuron_from_token, None)

        # Verify the response
        assert response.status_code == 429
        assert response.json()["detail"] == "Rate limit exceeded"

    @patch("app.routes.auth.RateLimitService.check_rate_limit")
    @patch("app.routes.auth.GoogleService.generate_pubsub_token")
    def test_get_pubsub_token_value_error(
        self, mock_generate_token, mock_check_rate_limit, client
    ):
        """Test Pub/Sub token generation with ValueError."""
        # Mock dependencies
        mock_check_rate_limit.return_value = True
        mock_generate_token.side_effect = ValueError("Rate limit exceeded")

        # Mock validator
        mock_validator = Neuron(
            hotkey="5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi",
            uid=0,
            stake=1000.0,
            trust=0.9,
            is_validator=True,
        )

        # Override the dependency
        from app.auth import get_neuron_from_token
        from main import app

        def mock_get_neuron_from_token():
            return mock_validator

        app.dependency_overrides[get_neuron_from_token] = (
            mock_get_neuron_from_token
        )

        try:
            # Make the request
            response = client.post(
                "/auth/pubsub-token", headers={"Authorization": "Bearer valid_token"}
            )
        finally:
            # Clean up
            app.dependency_overrides.pop(get_neuron_from_token, None)

        # Verify the response
        assert response.status_code == 400
        assert "Rate limit exceeded" in response.json()["detail"]

    @patch("app.routes.auth.RateLimitService.check_rate_limit")
    @patch("app.routes.auth.GoogleService.generate_pubsub_token")
    def test_get_pubsub_token_runtime_error(
        self, mock_generate_token, mock_check_rate_limit, client
    ):
        """Test Pub/Sub token generation with RuntimeError."""
        # Mock dependencies
        mock_check_rate_limit.return_value = True
        mock_generate_token.side_effect = RuntimeError("Service unavailable")

        # Mock validator
        mock_validator = Neuron(
            hotkey="5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi",
            uid=0,
            stake=1000.0,
            trust=0.9,
            is_validator=True,
        )

        # Override the dependency
        from app.auth import get_neuron_from_token
        from main import app

        def mock_get_neuron_from_token():
            return mock_validator

        app.dependency_overrides[get_neuron_from_token] = (
            mock_get_neuron_from_token
        )

        try:
            # Make the request
            response = client.post(
                "/auth/pubsub-token", headers={"Authorization": "Bearer valid_token"}
            )
        finally:
            # Clean up
            app.dependency_overrides.pop(get_neuron_from_token, None)

        # Verify the response
        assert response.status_code == 500
        assert "Service unavailable" in response.json()["detail"]

    def test_get_pubsub_token_unauthorized(self, client):
        """Test Pub/Sub token generation without authorization."""
        # Make the request without authorization header
        response = client.post("/auth/pubsub-token")

        # Verify the response (401 because dependency injection handles auth)
        assert response.status_code == 401

    def test_get_pubsub_token_invalid_token(self, client):
        """Test Pub/Sub token generation with invalid token."""
        # Make the request with invalid token
        response = client.post(
            "/auth/pubsub-token", headers={"Authorization": "Bearer invalid_token"}
        )

        # Verify the response (should be handled by dependency)
        assert response.status_code == 401

    @patch("app.routes.auth.RateLimitService.check_rate_limit")
    @patch("app.routes.auth.GoogleService.generate_pubsub_token")
    def test_get_pubsub_token_request_ip_extraction(
        self, mock_generate_token, mock_check_rate_limit, client
    ):
        """Test that client IP is correctly extracted for rate limiting."""
        # Mock dependencies
        mock_check_rate_limit.return_value = True
        mock_generate_token.return_value = ("token", 1234567890)

        # Mock validator
        mock_validator = Neuron(
            hotkey="5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi",
            uid=0,
            stake=1000.0,
            trust=0.9,
            is_validator=True,
        )

        # Override the dependency
        from app.auth import get_neuron_from_token
        from main import app

        def mock_get_neuron_from_token():
            return mock_validator

        app.dependency_overrides[get_neuron_from_token] = (
            mock_get_neuron_from_token
        )

        try:
            # Make the request
            response = client.post(
                "/auth/pubsub-token", headers={"Authorization": "Bearer valid_token"}
            )
        finally:
            # Clean up
            app.dependency_overrides.pop(get_neuron_from_token, None)

        # Verify rate limit was checked with client IP
        mock_check_rate_limit.assert_called_once()
        # The exact IP depends on the test client, but it should be called
