"""
Tests for custom route functionality.
"""

import asyncio
from unittest.mock import MagicMock, patch

import pytest
from fastapi import Request, Response

from app.routes.custom_route import TimedRoute


class TestTimedRoute:
    """Test cases for TimedRoute."""

    @pytest.mark.asyncio
    async def test_timed_route_adds_response_time_header(self):
        """Test that TimedRoute adds X-Response-Time header."""

        # Create a mock endpoint function
        async def mock_endpoint(request: Request) -> Response:
            # Simulate some processing time
            await asyncio.sleep(0.01)
            return Response(content="test response", status_code=200)

        # Create a TimedRoute instance
        route = TimedRoute("/test", endpoint=mock_endpoint, methods=["GET"])

        # Get the route handler
        handler = route.get_route_handler()

        # Create a mock request
        mock_request = MagicMock(spec=Request)

        # Call the handler
        with patch("app.routes.custom_route.time.time") as mock_time:
            # Mock time.time() to return predictable values
            mock_time.side_effect = [1000.0, 1000.1]  # 0.1 second difference

            response = await handler(mock_request)

        # Verify the response has the timing header
        assert "X-Response-Time" in response.headers
        # Use approximate comparison for floating point
        timing = float(response.headers["X-Response-Time"])
        assert abs(timing - 0.1) < 0.001  # Within 1ms tolerance

    @pytest.mark.asyncio
    async def test_timed_route_preserves_original_response(self):
        """Test that TimedRoute preserves the original response content and status."""

        # Create a mock endpoint function
        async def mock_endpoint(request: Request) -> Response:
            return Response(
                content="custom content",
                status_code=201,
                headers={"Custom-Header": "custom-value"},
            )

        # Create a TimedRoute instance
        route = TimedRoute("/test", endpoint=mock_endpoint, methods=["POST"])

        # Get the route handler
        handler = route.get_route_handler()

        # Create a mock request
        mock_request = MagicMock(spec=Request)

        # Call the handler
        response = await handler(mock_request)

        # Verify the original response is preserved
        assert response.status_code == 201
        assert response.body == b"custom content"
        assert response.headers["Custom-Header"] == "custom-value"

        # Verify the timing header was added
        assert "X-Response-Time" in response.headers

    @pytest.mark.asyncio
    async def test_timed_route_handles_exceptions(self):
        """Test that TimedRoute properly handles exceptions from the endpoint."""

        # Create a mock endpoint function that raises an exception
        async def mock_endpoint(request: Request) -> Response:
            raise ValueError("Test exception")

        # Create a TimedRoute instance
        route = TimedRoute("/test", endpoint=mock_endpoint, methods=["GET"])

        # Get the route handler
        handler = route.get_route_handler()

        # Create a mock request
        mock_request = MagicMock(spec=Request)

        # Call the handler and expect the exception to be raised
        with pytest.raises(ValueError, match="Test exception"):
            await handler(mock_request)

    @pytest.mark.asyncio
    async def test_timed_route_timing_accuracy(self):
        """Test that TimedRoute timing is reasonably accurate."""

        # Create a mock endpoint function with known delay
        async def mock_endpoint(request: Request) -> Response:
            return Response(content="test", status_code=200)

        # Create a TimedRoute instance
        route = TimedRoute("/test", endpoint=mock_endpoint, methods=["GET"])

        # Get the route handler
        handler = route.get_route_handler()

        # Create a mock request
        mock_request = MagicMock(spec=Request)

        # Call the handler with mocked time
        with patch("app.routes.custom_route.time.time") as mock_time:
            # Mock specific timing values
            mock_time.side_effect = [1000.0, 1000.25]  # 0.25 second difference

            response = await handler(mock_request)

        # Verify the timing header contains the expected value
        assert response.headers["X-Response-Time"] == "0.25"

    def test_get_route_handler_returns_callable(self):
        """Test that get_route_handler returns a callable."""

        # Create a simple endpoint function
        async def mock_endpoint(request: Request) -> Response:
            return Response(content="test", status_code=200)

        # Create a TimedRoute instance
        route = TimedRoute("/test", endpoint=mock_endpoint, methods=["GET"])

        # Get the route handler
        handler = route.get_route_handler()

        # Verify it's callable
        assert callable(handler)

        # Verify it's a coroutine function
        assert asyncio.iscoroutinefunction(handler)
