"""
Tests for system routes.
"""

from unittest.mock import MagicMock, patch


def test_simple_health_check(client):
    """Test the simple health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()

    # Check required fields
    assert data["status"] == "healthy"
    assert "timestamp" in data
    assert "version" in data
    assert "environment" in data
    assert "uptime_seconds" in data
    assert isinstance(data["uptime_seconds"], (int, float))


def test_detailed_health_check(client):
    """Test the detailed health check endpoint."""
    response = client.get("/system/health")
    assert response.status_code == 200
    data = response.json()

    # Check required fields
    assert data["status"] in ["healthy", "unhealthy"]
    assert "timestamp" in data
    assert "version" in data
    assert "environment" in data
    assert "uptime_seconds" in data
    assert "services" in data
    assert "config" in data

    # Check services
    services = data["services"]
    assert "database" in services
    assert "config" in services

    # Each service should have a status
    for service_name, service_data in services.items():
        assert "status" in service_data
        assert service_data["status"] in ["healthy", "unhealthy"]


def test_pubsub_status(client):
    """Test the Pub/Sub status endpoint."""
    # Create a mock for the GoogleService class
    mock_service = MagicMock()
    mock_service.verify_pubsub_setup.return_value = {
        "service_account_exists": True,
        "topic_exists": True,
        "permissions_valid": True,
    }

    # Patch the specific function that's being called in the route
    with patch("app.routes.system.GoogleService", return_value=mock_service):
        # Make the request
        response = client.get("/system/pubsub-status")

    # Check the response
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "ok"
    assert "checks" in data
    assert "config" in data
