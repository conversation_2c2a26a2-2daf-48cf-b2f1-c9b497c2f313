"""
Tests for development routes.
"""

from unittest.mock import MagicMock, patch

import pytest


class TestDevRoutes:
    """Test cases for development routes."""

    @patch("app.routes.dev.settings.ENVIRONMENT", "production")
    def test_dev_token_endpoint_production_environment(self, client):
        """Test dev token endpoint returns 404 in production."""
        response = client.post(
            "/auth/dev-token",
            json={"hotkey": "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"},
        )

        assert response.status_code == 404
        assert response.json()["detail"] == "Not found"

    @patch("app.routes.dev.settings.ENVIRONMENT", "production")
    def test_dev_pubsub_token_endpoint_production_environment(self, client):
        """Test dev pubsub token endpoint returns 404 in production."""
        response = client.post("/auth/dev-pubsub-token")

        assert response.status_code == 404
        assert response.json()["detail"] == "Not found"

    @patch("app.routes.dev.settings.ENVIRONMENT", "development")
    @patch("app.routes.dev.GoogleService.generate_pubsub_token")
    @patch("app.routes.dev.GoogleService.get_pubsub_topic_path")
    @pytest.mark.skip(reason="Database table creation issue in test environment")
    def test_dev_pubsub_token_success(
        self, mock_get_topic_path, mock_generate_token, client
    ):
        """Test successful dev pubsub token generation."""
        # Mock the services
        mock_generate_token.return_value = ("mock_pubsub_token", 1234567890)
        mock_get_topic_path.return_value = "projects/test/topics/test-topic"

        # Mock database query for existing validator
        mock_validator = MagicMock()
        mock_validator.hotkey = "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"
        mock_validator.uid = 0
        mock_validator.stake = 1000.0
        mock_validator.trust = 0.9

        # Mock the entire database dependency
        with patch("app.routes.dev.Depends") as mock_depends:
            mock_db_session = MagicMock()
            mock_depends.return_value = mock_db_session
            mock_db_session.query.return_value.filter.return_value.first.return_value = mock_validator

            # Override the get_db dependency
            from app.db import get_db
            from main import app

            def mock_get_db():
                return mock_db_session

            app.dependency_overrides[get_db] = mock_get_db

            try:
                response = client.post("/auth/dev-pubsub-token")
            finally:
                app.dependency_overrides.pop(get_db, None)

        assert response.status_code == 200
        data = response.json()
        assert data["access_token"] == "dev_pubsub_token_for_emulator_testing"
        assert data["token_type"] == "Bearer"
        assert data["scope"] == "https://www.googleapis.com/auth/pubsub"
        assert "topic_paths" in data
        assert "allocation_events" in data["topic_paths"]
        assert "miner_events" in data["topic_paths"]
        assert "system_events" in data["topic_paths"]
        assert "validation_events" in data["topic_paths"]
        assert "expires_in" in data

    @patch("app.routes.dev.settings.ENVIRONMENT", "development")
    @patch("app.routes.dev.GoogleService.generate_pubsub_token")
    @pytest.mark.skip(reason="Database table creation issue in test environment")
    def test_dev_pubsub_token_new_validator(self, mock_generate_token, client):
        """Test dev pubsub token with new validator creation."""
        # Mock the services
        mock_generate_token.return_value = ("mock_pubsub_token", 1234567890)

        with patch(
            "app.routes.dev.GoogleService.get_pubsub_topic_path"
        ) as mock_get_topic_path:
            mock_get_topic_path.return_value = "projects/test/topics/test-topic"

            # Mock database session
            mock_db_session = MagicMock()
            mock_db_session.query.return_value.filter.return_value.first.return_value = None

            # Override the get_db dependency
            from app.db import get_db
            from main import app

            def mock_get_db():
                return mock_db_session

            app.dependency_overrides[get_db] = mock_get_db

            try:
                response = client.post("/auth/dev-pubsub-token")
            finally:
                app.dependency_overrides.pop(get_db, None)

        assert response.status_code == 200

        # Verify new validator was added to database
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()

    @patch("app.routes.dev.settings.ENVIRONMENT", "development")
    @patch("app.routes.dev.GoogleService.generate_pubsub_token")
    @pytest.mark.skip(reason="Database table creation issue in test environment")
    def test_dev_pubsub_token_service_error(self, mock_generate_token, client):
        """Test dev pubsub token when service raises error."""
        # Mock the service to raise an error
        mock_generate_token.side_effect = Exception("Service error")

        # Mock database session
        mock_db_session = MagicMock()
        mock_db_session.query.return_value.filter.return_value.first.return_value = None

        # Override the get_db dependency
        from app.db import get_db
        from main import app

        def mock_get_db():
            return mock_db_session

        app.dependency_overrides[get_db] = mock_get_db

        try:
            response = client.post("/auth/dev-pubsub-token")
        finally:
            app.dependency_overrides.pop(get_db, None)

        assert response.status_code == 500
        assert "Service error" in response.json()["detail"]

    @patch("app.routes.dev.settings.ENVIRONMENT", "development")
    @patch("app.routes.dev.TokenService.create_token")
    def test_dev_token_request_body_parsing(
        self, mock_create_token, client
    ):
        """Test dev token endpoint with different request body formats."""
        # Mock the token service
        mock_token = MagicMock()
        mock_token.access_token = "mock_token"
        mock_token.token_type = "bearer"
        mock_create_token.return_value = mock_token

        # Mock database session
        mock_db_session = MagicMock()

        # Override the get_db dependency
        from app.db import get_db
        from main import app

        def mock_get_db():
            return mock_db_session

        app.dependency_overrides[get_db] = mock_get_db

        try:
            # Test with valid hotkey
            response = client.post(
                "/auth/dev-token",
                json={"hotkey": "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"},
            )
        finally:
            app.dependency_overrides.pop(get_db, None)

        assert response.status_code == 200
        data = response.json()
        assert data["access_token"] == "mock_token"
        assert data["token_type"] == "bearer"
        assert data["expires_in"] == 3600
        assert "expires_in" in data

    @patch("app.routes.dev.settings.ENVIRONMENT", "development")
    @pytest.mark.skip(reason="Database table creation issue in test environment")
    def test_dev_token_missing_hotkey(self, client):
        """Test dev token endpoint with missing hotkey in request."""
        response = client.post("/auth/dev-token", json={})

        # This should return a validation error
        assert response.status_code == 422  # Unprocessable Entity

    @patch("app.routes.dev.settings.ENVIRONMENT", "development")
    @patch("app.routes.dev.TokenService.create_token")
    @patch("app.routes.dev.time.time")
    def test_dev_token_timing_values(
        self, mock_time, mock_create_token, client
    ):
        """Test that dev token endpoint returns correct timing values."""
        # Mock time and token service
        mock_time.return_value = 1000000
        mock_token = MagicMock()
        mock_token.access_token = "mock_token"
        mock_token.token_type = "bearer"
        mock_create_token.return_value = mock_token

        # Mock database session
        mock_db_session = MagicMock()

        # Override the get_db dependency
        from app.db import get_db
        from main import app

        def mock_get_db():
            return mock_db_session

        app.dependency_overrides[get_db] = mock_get_db

        try:
            with patch("app.routes.dev.settings.TOKEN_EXPIRY", 3600):
                response = client.post(
                    "/auth/dev-token",
                    json={"hotkey": "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"},
                )
        finally:
            app.dependency_overrides.pop(get_db, None)

        assert response.status_code == 200
        data = response.json()
        assert data["expires_in"] == 3600
