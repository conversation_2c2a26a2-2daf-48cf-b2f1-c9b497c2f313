"""
Tests for validator routes.
"""

from unittest.mock import MagicMock, patch

import jwt

from core.config import settings


def test_get_validator_info(client, mock_validator):
    """Test getting validator info with a mocked signature verification."""
    # Mock all the dependencies properly
    mock_subtensor = MagicMock()
    mock_metagraph = MagicMock()

    with (
        patch(
            "app.auth.BittensorService.get_components",
            return_value=(mock_subtensor, mock_metagraph),
        ),
        patch(
            "app.auth.BittensorService.is_validator",
            return_value=(True, 0, 1000.0, 0.9),
        ),
        patch("app.auth.BittensorService.verify_signature", return_value=True),
        patch("app.auth.NeuronService.get_or_create_neuron"),
        patch("app.auth.NeuronService.to_pydantic", return_value=mock_validator),
    ):
        # Make the request
        hotkey = "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"
        response = client.get(
            "/validators/me",
            headers={"Authorization": f"Bittensor {hotkey}:mock_signature"},
        )

    # Check the response
    assert response.status_code == 200
    data = response.json()
    assert data["hotkey"] == "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"
    assert data["uid"] == 0
    assert data["stake"] == 1000.0
    assert data["trust"] == 0.9


def test_get_validator_info_from_token(client):
    """Test getting validator info with a JWT token."""
    from app.auth import get_neuron_from_token
    from core.models import Neuron
    from main import app

    # Create a token
    token = jwt.encode(
        {"sub": "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"},
        settings.JWT_SECRET,
        algorithm="HS256",
    )

    # Create a mock validator object
    mock_validator = Neuron(
        hotkey="5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi",
        uid=0,
        stake=1000.0,
        trust=0.9,
        is_validator=True,
    )

    # Override the dependency to return our mock validator
    def mock_get_neuron_from_token():
        return mock_validator

    app.dependency_overrides[get_neuron_from_token] = mock_get_neuron_from_token

    try:
        # Make the request with the token
        response = client.get(
            "/validators/me/token", headers={"Authorization": f"Bearer {token}"}
        )
    finally:
        # Clean up the dependency override
        app.dependency_overrides.pop(get_neuron_from_token, None)

    # Check the response
    assert response.status_code == 200
    data = response.json()
    assert data["hotkey"] == "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"
    assert data["uid"] == 0
    assert data["stake"] == 1000.0
    assert data["trust"] == 0.9
