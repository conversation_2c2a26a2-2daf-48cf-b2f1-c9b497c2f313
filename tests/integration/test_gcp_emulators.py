"""
Integration tests for GCP emulators.
These tests verify that the application works correctly with local GCP emulators.
"""

import os
import socket

import pytest
import redis
from google.api_core import exceptions as gcp_exceptions
from google.cloud import pubsub_v1


def is_port_open(host: str, port: int, timeout: float = 1.0) -> bool:
    """Check if a port is open on a host."""
    try:
        with socket.create_connection((host, port), timeout=timeout):
            return True
    except (socket.timeout, socket.error):
        return False


def skip_if_emulator_not_running(host: str, port: int, name: str):
    """Skip test if emulator is not running."""
    return pytest.mark.skipif(
        not is_port_open(host, port),
        reason=f"{name} emulator not running on {host}:{port}",
    )


class TestPubSubEmulator:
    """Test Pub/Sub emulator integration."""

    @pytest.fixture(autouse=True)
    def setup_emulator(self):
        """Setup Pub/Sub emulator environment."""
        # Set emulator host if not already set
        if not os.getenv("PUBSUB_EMULATOR_HOST"):
            os.environ["PUBSUB_EMULATOR_HOST"] = "localhost:8085"

        if not os.getenv("GCP_PROJECT_ID"):
            os.environ["GCP_PROJECT_ID"] = "local-project"

    @skip_if_emulator_not_running("localhost", 8085, "Pub/Sub")
    def test_pubsub_emulator_connection(self):
        """Test connection to Pub/Sub emulator."""
        project_id = os.getenv("GCP_PROJECT_ID", "local-project")

        # Create publisher client
        publisher = pubsub_v1.PublisherClient()

        # Test basic connectivity by listing topics
        project_path = f"projects/{project_id}"

        try:
            topics = list(publisher.list_topics(request={"project": project_path}))
            assert isinstance(topics, list)
        except gcp_exceptions.NotFound:
            # It's okay if no topics exist yet
            pass

    @skip_if_emulator_not_running("localhost", 8085, "Pub/Sub")
    def test_create_topic_and_subscription(self):
        """Test creating topics and subscriptions."""
        project_id = os.getenv("GCP_PROJECT_ID", "local-project")
        topic_name = "test-topic"
        subscription_name = "test-subscription"

        publisher = pubsub_v1.PublisherClient()
        subscriber = pubsub_v1.SubscriberClient()

        topic_path = publisher.topic_path(project_id, topic_name)
        subscription_path = subscriber.subscription_path(project_id, subscription_name)

        # Create topic
        try:
            topic = publisher.create_topic(request={"name": topic_path})
            assert topic.name == topic_path
        except gcp_exceptions.AlreadyExists:
            # Topic already exists, that's fine
            pass

        # Create subscription
        try:
            subscription = subscriber.create_subscription(
                request={"name": subscription_path, "topic": topic_path}
            )
            assert subscription.name == subscription_path
        except gcp_exceptions.AlreadyExists:
            # Subscription already exists, that's fine
            pass

    @skip_if_emulator_not_running("localhost", 8085, "Pub/Sub")
    def test_publish_and_receive_message(self):
        """Test publishing and receiving messages."""
        project_id = os.getenv("GCP_PROJECT_ID", "local-project")
        topic_name = "test-messages"
        subscription_name = "test-messages-sub"

        publisher = pubsub_v1.PublisherClient()
        subscriber = pubsub_v1.SubscriberClient()

        topic_path = publisher.topic_path(project_id, topic_name)
        subscription_path = subscriber.subscription_path(project_id, subscription_name)

        # Ensure topic and subscription exist
        try:
            publisher.create_topic(request={"name": topic_path})
        except gcp_exceptions.AlreadyExists:
            pass

        try:
            subscriber.create_subscription(
                request={"name": subscription_path, "topic": topic_path}
            )
        except gcp_exceptions.AlreadyExists:
            pass

        # Publish a message
        message_data = "Test message from validator-token-gateway"
        future = publisher.publish(topic_path, message_data.encode("utf-8"))
        message_id = future.result()

        assert message_id is not None

        # Pull messages
        response = subscriber.pull(
            request={"subscription": subscription_path, "max_messages": 1}
        )

        if response.received_messages:
            message = response.received_messages[0]
            assert message.message.data.decode("utf-8") == message_data

            # Acknowledge the message
            subscriber.acknowledge(
                request={"subscription": subscription_path, "ack_ids": [message.ack_id]}
            )


class TestGoogleServiceWithEmulator:
    """Test GoogleService with emulator."""

    @pytest.fixture(autouse=True)
    def setup_emulator(self):
        """Setup emulator environment."""
        os.environ["PUBSUB_EMULATOR_HOST"] = "localhost:8085"
        os.environ["GCP_PROJECT_ID"] = "local-project"

    @skip_if_emulator_not_running("localhost", 8085, "Pub/Sub")
    def test_google_service_pubsub_integration(self):
        """Test GoogleService with Pub/Sub emulator."""
        # Test the topic path generation directly with mock settings
        from unittest.mock import patch

        from core.services.google_service import GoogleService

        # Mock the settings to have the required values
        with patch("core.services.google_service.settings") as mock_settings:
            mock_settings.GCP_PROJECT_ID = "local-project"
            mock_settings.GCP_PUBSUB_ALLOCATION_EVENTS = "allocation-events"
            mock_settings.GCP_PUBSUB_MINER_EVENTS = "miner-events"
            mock_settings.GCP_PUBSUB_SYSTEM_EVENTS = "system-events"
            mock_settings.GCP_PUBSUB_VALIDATION_EVENTS = "validation-events"

            # Test getting topic paths
            topic_paths = GoogleService.get_pubsub_topic_paths()
            assert "local-project" in topic_paths["allocation_events"]
            assert topic_paths["allocation_events"] == "projects/local-project/topics/allocation-events"
            assert topic_paths["miner_events"] == "projects/local-project/topics/miner-events"
            assert topic_paths["system_events"] == "projects/local-project/topics/system-events"
            assert topic_paths["validation_events"] == "projects/local-project/topics/validation-events"

        # Also test that the method works with empty settings (should not crash)
        with patch("core.services.google_service.settings") as mock_settings:
            mock_settings.GCP_PROJECT_ID = ""
            mock_settings.GCP_PUBSUB_ALLOCATION_EVENTS = ""
            mock_settings.GCP_PUBSUB_MINER_EVENTS = ""
            mock_settings.GCP_PUBSUB_SYSTEM_EVENTS = ""
            mock_settings.GCP_PUBSUB_VALIDATION_EVENTS = ""

            topic_paths = GoogleService.get_pubsub_topic_paths()
            assert topic_paths["allocation_events"] == "projects//topics/"


class TestSecretManagerMock:
    """Test Secret Manager mock integration."""

    @skip_if_emulator_not_running("localhost", 6380, "Secret Manager Mock")
    def test_secret_manager_mock_connection(self):
        """Test connection to Secret Manager mock (Redis)."""
        # Connect to Secret Manager mock
        r = redis.Redis(host="localhost", port=6380, decode_responses=True)

        # Test basic connectivity
        assert r.ping() is True

        # Test setting and getting a secret
        secret_name = "projects/local-project/secrets/test-secret/versions/latest"
        secret_value = "test-secret-value"

        r.set(secret_name, secret_value)
        retrieved_value = r.get(secret_name)

        assert retrieved_value == secret_value

    @skip_if_emulator_not_running("localhost", 6380, "Secret Manager Mock")
    def test_predefined_secrets(self):
        """Test that we can set up and retrieve secrets from the mock."""
        # Connect to Secret Manager mock
        r = redis.Redis(host="localhost", port=6380, decode_responses=True)

        # Set up predefined secrets (simulating what would be in a real environment)
        secrets = {
            "projects/local-project/secrets/jwt-secret/versions/latest": "test-jwt-secret",
            "projects/local-project/secrets/db-password/versions/latest": "test-db-password",
            "projects/local-project/secrets/redis-password/versions/latest": "test-redis-password",
        }

        # Set the secrets
        for secret_name, secret_value in secrets.items():
            r.set(secret_name, secret_value)

        # Verify we can retrieve them
        for secret_name, expected_value in secrets.items():
            retrieved_value = r.get(secret_name)
            assert retrieved_value == expected_value, (
                f"Secret {secret_name} not found or incorrect"
            )

        # Clean up
        for secret_name in secrets.keys():
            r.delete(secret_name)


@pytest.mark.integration
class TestFullGCPIntegration:
    """Full integration tests with all GCP emulators."""

    @pytest.fixture(autouse=True)
    def setup_full_environment(self):
        """Setup full GCP emulator environment."""
        os.environ.update(
            {
                "PUBSUB_EMULATOR_HOST": "localhost:8085",
                "GCP_PROJECT_ID": "local-project",
                "SECRET_MANAGER_EMULATOR_HOST": "localhost:6380",
                "ENVIRONMENT": "local-gcp",
            }
        )

    @pytest.mark.skip(reason="Requires API to be running - integration test")
    def test_end_to_end_token_flow_with_emulators(self):
        """Test complete token flow with GCP emulators."""
        # This would test the full flow:
        # 1. Authenticate validator
        # 2. Generate token
        # 3. Use token to publish to Pub/Sub emulator
        # 4. Verify message was published

        # Note: This test would require the API to be running
        # and configured to use the emulators
        pass

    @skip_if_emulator_not_running("localhost", 8085, "Pub/Sub")
    def test_terraform_equivalent_setup(self):
        """Test that local setup matches Terraform configuration."""
        # This test verifies that the local emulator setup
        # matches what would be created by Terraform

        # Check that required topics exist
        project_id = "local-project"
        publisher = pubsub_v1.PublisherClient()

        project_path = f"projects/{project_id}"
        topics = list(publisher.list_topics(request={"project": project_path}))

        topic_names = [topic.name.split("/")[-1] for topic in topics]

        # Verify expected topics exist (matching Terraform config)
        expected_topics = [
            "validator-token-gateway-messages",
            "validator-notifications",
        ]
        for expected_topic in expected_topics:
            # Skip assertion if topics don't exist - this is just a connectivity test
            if expected_topic not in topic_names:
                pytest.skip(
                    f"Topic {expected_topic} not found - emulator may not be fully configured"
                )
