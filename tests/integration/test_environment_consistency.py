"""
Integration tests to verify environment consistency between local and production.

These tests ensure that the core business logic behaves identically
in both local development (FastAPI) and production (Cloud Functions).
"""

from unittest.mock import Mock, patch

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from core.config import settings
from core.models import Base, Neuron, NeuronModel
from core.services import (
    AuthService,
    BittensorService,
    NeuronService,
    TokenService,
)


@pytest.fixture
def test_db():
    """Create a temporary test database."""
    # Create temporary database
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)

    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = SessionLocal()

    yield session

    session.close()


@pytest.fixture
def mock_validator():
    """Create a mock validator for testing."""
    return Neuron(
        hotkey="5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY",
        uid=1,
        stake=1000.0,
        trust=0.95,
        is_validator=True,
    )


@pytest.fixture
def mock_metagraph():
    """Create a mock Bittensor metagraph."""
    metagraph = Mock()
    metagraph.hotkeys = ["5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY"]
    metagraph.S = [1000.0]  # Stake
    metagraph.T = [0.95]    # Trust
    metagraph.weights = {
        0: Mock(
            sum=Mock(return_value=Mock(
            item=Mock(return_value=1))))}
    return metagraph


class TestEnvironmentConsistency:
    """Test suite for environment consistency."""

    def test_config_consistency(self):
        """Test that configuration is consistent across environments."""
        # Test that settings can be imported and initialized
        assert settings is not None
        assert hasattr(settings, 'ENVIRONMENT')
        assert hasattr(settings, 'active_netuid')
        assert hasattr(settings, 'active_network')
        assert hasattr(settings, 'auth_message')

    def test_model_consistency(self):
        """Test that database models are consistent."""
        # Test that models can be imported and used
        validator = Neuron(
            hotkey="test_hotkey",
            uid=1,
            stake=100.0,
            trust=0.5,
            is_validator=True,
        )

        assert validator.hotkey == "test_hotkey"
        assert validator.uid == 1
        assert validator.stake == 100.0
        assert validator.trust == 0.5
        assert validator.is_validator

    def test_service_imports(self):
        """Test that all core services can be imported."""
        # This test ensures the core library is properly structured
        assert AuthService is not None
        assert BittensorService is not None
        assert TokenService is not None
        assert NeuronService is not None

    @patch('core.services.bittensor_service.bittensor')
    def test_bittensor_service_consistency(self, mock_bittensor, mock_metagraph):
        """Test that BittensorService behaves consistently."""
        # Mock bittensor components
        mock_subtensor = Mock()
        mock_bittensor.subtensor.return_value = mock_subtensor
        mock_bittensor.metagraph.return_value = mock_metagraph

        # Test get_components
        subtensor, metagraph = BittensorService.get_components()
        assert subtensor == mock_subtensor
        assert metagraph == mock_metagraph

        # Test is_validator
        is_validator, uid, stake, trust = BittensorService.is_validator(
            mock_metagraph, "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY"
        )
        assert is_validator is True
        assert uid == 0
        assert stake == 1000.0
        assert trust == 0.95

    def test_validator_service_consistency(self, test_db, mock_validator):
        """Test that NeuronService behaves consistently."""
        # Test get_or_create_neuron
        validator_db = NeuronService.get_or_create_neuron(
            test_db,
            mock_validator.hotkey,
            mock_validator.uid,
            mock_validator.stake,
            mock_validator.trust,
            mock_validator.is_validator
        )

        assert validator_db.hotkey == mock_validator.hotkey
        assert validator_db.uid == mock_validator.uid
        assert validator_db.stake == mock_validator.stake
        assert validator_db.trust == mock_validator.trust

        # Test to_pydantic
        pydantic_validator = NeuronService.to_pydantic(validator_db)
        assert isinstance(pydantic_validator, Neuron)
        assert pydantic_validator.hotkey == mock_validator.hotkey

    def test_token_service_consistency(self, test_db, mock_validator):
        """Test that TokenService behaves consistently."""
        # Create a validator in the database first
        validator_db = NeuronModel(
            hotkey=mock_validator.hotkey,
            uid=mock_validator.uid,
            stake=mock_validator.stake,
            trust=mock_validator.trust
        )
        test_db.add(validator_db)
        test_db.commit()

        # Test create_token
        token = TokenService.create_token(mock_validator, test_db)
        assert token.access_token is not None
        assert token.token_type == "bearer"

        # Test validate_token
        is_valid, payload = TokenService.validate_token(token.access_token, test_db)
        assert is_valid is True
        assert payload["sub"] == mock_validator.hotkey
        assert payload["uid"] == mock_validator.uid

    @patch('core.services.bittensor_service.bittensor')
    def test_auth_service_consistency(self, mock_bittensor, test_db, mock_metagraph):
        """Test that AuthService behaves consistently."""
        # Mock bittensor components
        mock_subtensor = Mock()
        mock_bittensor.subtensor.return_value = mock_subtensor
        mock_bittensor.metagraph.return_value = mock_metagraph

        # Mock signature verification
        mock_keypair = Mock()
        mock_keypair.verify.return_value = True
        mock_bittensor.Keypair.return_value = mock_keypair

        # Test authenticate_validator_with_signature
        validator = AuthService.authenticate_validator_with_signature(
            "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY",
            "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
            test_db
        )

        assert validator is not None
        assert validator.hotkey == "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY"

    def test_auth_header_parsing_consistency(self):
        """Test that auth header parsing is consistent."""
        # Test Bittensor auth header parsing
        auth_header = "Bittensor hotkey:signature"
        result = AuthService.parse_bittensor_auth_header(auth_header)
        assert result == ("hotkey", "signature")

        # Test Bearer token parsing
        bearer_header = "Bearer token123"
        result = AuthService.parse_bearer_token(bearer_header)
        assert result == "token123"

        # Test invalid headers
        assert AuthService.parse_bittensor_auth_header("Invalid header") is None
        assert AuthService.parse_bearer_token("Invalid header") is None

    def test_environment_detection(self):
        """Test that environment detection works consistently."""
        # Test environment properties
        assert hasattr(settings, 'is_production')
        assert hasattr(settings, 'is_development')

        # These should be boolean values
        assert isinstance(settings.is_production, bool)
        assert isinstance(settings.is_development, bool)

        # They should be mutually exclusive in most cases
        if settings.ENVIRONMENT == "production":
            assert settings.is_production is True
            assert settings.is_development is False
        elif settings.ENVIRONMENT in ["development", "testing", "local-gcp"]:
            assert settings.is_development is True
            assert settings.is_production is False

    def test_database_model_consistency(self, test_db):
        """Test that database models work consistently."""
        # Create a validator
        validator = NeuronModel(
            hotkey="test_hotkey",
            uid=1,
            stake=100.0,
            trust=0.5
        )
        test_db.add(validator)
        test_db.commit()

        # Query it back
        retrieved = test_db.query(NeuronModel).filter(
            NeuronModel.hotkey == "test_hotkey"
        ).first()

        assert retrieved is not None
        assert retrieved.hotkey == "test_hotkey"
        assert retrieved.uid == 1
        assert retrieved.stake == 100.0
        assert retrieved.trust == 0.5

    def test_error_handling_consistency(self, test_db):
        """Test that error handling is consistent across services."""
        # Test invalid token validation
        is_valid, payload = TokenService.validate_token("invalid_token", test_db)
        assert is_valid is False
        assert payload == {}

        # Test invalid validator authentication
        validator = AuthService.authenticate_validator_with_signature(
            "invalid_hotkey",
            "invalid_signature",
            test_db
        )
        assert validator is None

    def test_configuration_environment_awareness(self):
        """Test that configuration adapts to environment correctly."""
        # Test that active_netuid changes based on environment
        original_env = settings.ENVIRONMENT

        # Mock development environment
        with patch.object(settings, 'ENVIRONMENT', 'development'):
            assert settings.active_netuid == settings.BITTENSOR_SANDBOX_NETUID
            assert settings.active_network == "test"

        # Mock production environment
        with patch.object(settings, 'ENVIRONMENT', 'production'):
            assert settings.active_netuid == settings.BITTENSOR_NETUID
            assert settings.active_network == settings.BITTENSOR_NETWORK
