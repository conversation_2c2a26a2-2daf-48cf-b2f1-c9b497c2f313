# Development Guide

This guide helps developers get started with the Validator Token Gateway and provides common maintenance tasks.

## Quick Start

### 1. Initial Setup
```bash
# Clone the repository
git clone <repository-url>
cd validator-token-gateway

# Set up development environment (installs dependencies and pre-commit hooks)
make setup-dev

# Check environment status
make status
```

### 2. Start Development
```bash
# Start the development server
make dev

# Or start with Docker (includes database and Redis)
make docker-up
```

The API will be available at:
- **API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Database**: localhost:5432
- **Redis**: localhost:6379

## Common Development Tasks

### Testing
```bash
# Run all tests
make test

# Run tests with coverage report
make test-cov

# Run tests in watch mode (re-runs on file changes)
make test-watch

# Run specific test categories
make test-unit          # Unit tests only
make test-integration   # Integration tests only
make test-routes        # Route tests only
make test-services      # Service tests only
```

### Code Quality
```bash
# Check code formatting and linting
make lint

# Auto-fix linting issues
make lint-fix

# Format code
make format

# Run all pre-commit hooks
make pre-commit

# Quick development checks (lint + format + test)
make quick-check

# Comprehensive checks (everything)
make full-check
```

### Docker Development
```bash
# Start all services
make docker-up

# View logs
make docker-logs
make docker-logs-api    # API logs only

# Stop services
make docker-down

# Clean up Docker resources
make docker-clean

# Open shell in API container
make docker-shell
```

### Database Operations
```bash
# Initialize database tables
make init-db

# Open database shell
make db-shell

# Backup database
make backup-db

# Restore database
make restore-db BACKUP=backup_20231201_143022.sql
```

### Local GCP Testing
```bash
# Setup complete local GCP environment (like LocalStack for AWS)
make local-demo

# Start only GCP emulators
make setup-local-gcp

# Check emulator status
make gcp-status

# Test Pub/Sub connectivity
make test-pubsub-emulator

# Clean up GCP resources
make clean-gcp
```

### Validator Ecosystem Demo
```bash
# 🎯 RECOMMENDED: Complete local demo
make local-demo

# Start individual validators (in separate terminals)
make run-validator1    # Validator Alpha
make run-validator2    # Validator Beta

# Monitor real-time communication
make monitor-pubsub

# Quick automated demos
make demo              # 2-minute demo
make demo-short        # 30-second demo
```

## Project Structure

```
validator-token-gateway/
├── app/                    # Main application code
│   ├── routes/            # API route handlers
│   ├── services/          # Business logic services
│   ├── auth.py           # Authentication functions
│   ├── config.py         # Configuration settings
│   ├── db.py             # Database configuration
│   └── models.py         # Data models
├── tests/                 # Test suite (mirrors app structure)
│   ├── routes/           # Route tests
│   ├── services/         # Service tests
│   └── utils/            # Test utilities
├── terraform/            # Infrastructure as code
├── Makefile              # Development commands
├── docker-compose.yml    # Local development stack
├── pyproject.toml        # Python dependencies and config
└── main.py              # FastAPI application entry point
```

## Testing Guidelines

### Test Structure
Tests are organized to mirror the application structure:
- `tests/routes/` → tests for `app/routes/`
- `tests/services/` → tests for `app/services/`
- `tests/test_*.py` → tests for `app/*.py`

### Coverage Target
- **Target**: 80% minimum coverage
- **Current**: 89% coverage ✅
- Run `make test-cov` to see detailed coverage report

### Writing Tests
```python
# Example test structure
class TestServiceName:
    """Test cases for ServiceName."""

    def test_method_success(self):
        """Test successful operation."""
        # Arrange
        # Act
        # Assert

    def test_method_error_case(self):
        """Test error handling."""
        # Test error scenarios
```

## Code Style

### Formatting
- **Tool**: Ruff (replaces Black + isort + flake8)
- **Line length**: 88 characters
- **Quote style**: Double quotes
- **Import sorting**: Automatic

### Pre-commit Hooks
Pre-commit hooks run automatically on `git commit`:
- Code formatting (ruff format)
- Linting (ruff check)
- Import sorting

## Package Management

This project uses **uv** instead of Poetry for package management. uv provides:
- Faster dependency resolution (10-100x faster than Poetry)
- Standard Python packaging format
- Reliable lock file management
- Simple, familiar commands

### uv vs Poetry Command Comparison
| Task | uv | Poetry (old) |
|------|----|----|
| Install dependencies | `uv sync` | `poetry install` |
| Add dependency | `uv add package` | `poetry add package` |
| Remove dependency | `uv remove package` | `poetry remove package` |
| Run command | `uv run command` | `poetry run command` |
| Show dependencies | `uv tree` | `poetry show --tree` |

All Makefile commands have been updated to use uv automatically.

## Environment Variables

### Development
```bash
# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/validator_gateway

# JWT
JWT_SECRET=dev_jwt_secret_for_testing_only
TOKEN_EXPIRY=3600

# Bittensor
BITTENSOR_NETWORK=test
BITTENSOR_NETUID=27
BITTENSOR_SANDBOX_NETUID=15

# Environment
ENVIRONMENT=development
```

### Testing
```bash
# Set testing mode
TESTING=True
```

## Troubleshooting

### Common Issues

#### uv Issues
```bash
# Clear uv cache
uv cache clean

# Reinstall dependencies
rm uv.lock
make install-dev
```

#### Docker Issues
```bash
# Clean up Docker resources
make docker-clean

# Rebuild containers
make docker-build
```

#### Database Issues
```bash
# Reset database
make docker-down
docker volume rm validator-token-gateway_postgres_data
make docker-up
make init-db
```

#### Test Issues
```bash
# Clear test cache
make clean

# Run tests with verbose output
uv run pytest -v -s
```

### Getting Help
```bash
# Show all available commands
make help

# Show project information
make info

# Check environment status
make status
```

## Deployment

### Infrastructure
```bash
# Initialize Terraform
make tf-init

# Plan infrastructure changes
make tf-plan

# Apply infrastructure
make tf-apply

# Destroy infrastructure
make tf-destroy
```

### CI/CD
```bash
# Run CI tests
make ci-test

# Run CI linting
make ci-lint
```

## Contributing

1. **Setup**: Run `make setup-dev`
2. **Development**: Use `make dev` for local development
3. **Testing**: Ensure `make test-cov` passes with 80%+ coverage
4. **Code Quality**: Run `make quick-check` before committing
5. **Pre-commit**: Hooks will run automatically on commit

## Useful Commands Reference

| Command | Description |
|---------|-------------|
| `make help` | Show all available commands |
| `make setup-dev` | Complete development setup |
| `make dev` | Start development server |
| `make test-cov` | Run tests with coverage |
| `make quick-check` | Fast quality checks |
| `make docker-up` | Start all services |
| `make clean` | Clean up cache files |
| `make info` | Show project information |
