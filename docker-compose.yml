version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/validator_gateway
      - JWT_SECRET=dev_jwt_secret_for_testing_only
      - BITTENSOR_NETWORK=test
      - BITTENSOR_NETUID=27
      - BITTENSOR_SANDBOX_NETUID=15
      - ENVIRONMENT=development
      # GCP Emulator settings
      - PUBSUB_EMULATOR_HOST=pubsub-emulator:8085
      - PUBSUB_PROJECT_ID=local-project
      - GCP_PROJECT_ID=local-project
      - GOOGLE_APPLICATION_CREDENTIALS=/app/local-service-account.json
    depends_on:
      - db
      - pubsub-emulator
    volumes:
      - ./app:/app/app
      - ./main.py:/app/main.py
      - ./local-service-account.json:/app/local-service-account.json:ro
    restart: unless-stopped
    command: >
      bash -c "
        uv run python -m init_db &&
        uv run uvicorn main:app --host 0.0.0.0 --port 8000 --reload
      "

  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=validator_gateway
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5



  # Google Cloud Pub/Sub Emulator
  pubsub-emulator:
    image: gcr.io/google.com/cloudsdktool/cloud-sdk:latest
    ports:
      - "8085:8085"
    command: >
      bash -c "
        gcloud beta emulators pubsub start --host-port=0.0.0.0:8085 --project=local-project
      "
    restart: unless-stopped

  # Secret Manager Mock (simple key-value store)
  secret-manager-mock:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    command: redis-server --port 6379
    restart: unless-stopped

volumes:
  postgres_data:
