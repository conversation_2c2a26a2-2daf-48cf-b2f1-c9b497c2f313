# Documentation Index

Quick navigation to all project documentation.

## 📖 Core Documentation

### [README.md](./README.md)
**Main project documentation** - Start here for overview, quick start, and deployment instructions.

### [DEVELOPMENT.md](./DEVELOPMENT.md)
**Developer guide** - Complete development setup, testing, and maintenance tasks.

### [QUICK_REFERENCE.md](./QUICK_REFERENCE.md)
**Command cheat sheet** - Quick reference for daily development commands.

## 🚀 Deployment & Infrastructure

### [functions/auth_function/README.md](./functions/auth_function/README.md)
**Cloud Function deployment** - Production deployment guide and configuration.

### [GCP_TESTING.md](./GCP_TESTING.md)
**Local GCP testing** - Test with GCP emulators before cloud deployment.

## 🎭 Demo & Testing

### [VALIDATOR_DEMO.md](./VALIDATOR_DEMO.md)
**Live validator demo** - Real-time Pub/Sub communication demonstration.

## 📊 Project Information

### [PROJECT_STATUS.md](./PROJECT_STATUS.md)
**Current project status** - Architecture overview, metrics, and health.

### [CHANGELOG.md](./CHANGELOG.md)
**Version history** - Major changes and release notes.

## 🎯 Quick Start Paths

### New Developer
1. [README.md](./README.md) - Project overview
2. [DEVELOPMENT.md](./DEVELOPMENT.md) - Setup development environment
3. [QUICK_REFERENCE.md](./QUICK_REFERENCE.md) - Daily commands

### Deployment
1. [README.md](./README.md#deployment) - Quick deployment
2. [functions/auth_function/README.md](./functions/auth_function/README.md) - Detailed deployment
3. [GCP_TESTING.md](./GCP_TESTING.md) - Test before deploy

### Demo & Testing
1. [VALIDATOR_DEMO.md](./VALIDATOR_DEMO.md) - Live demo
2. [GCP_TESTING.md](./GCP_TESTING.md) - Local testing
3. [DEVELOPMENT.md](./DEVELOPMENT.md#testing) - Test commands

---

**Total Documentation**: 8 focused, essential files
**Status**: ✅ Clean and organized
