# Validator Token Gateway

[![Version](https://img.shields.io/badge/version-2.0.0-blue)](./CHANGELOG.md)
[![Coverage](https://img.shields.io/badge/coverage-89%25-brightgreen)](./htmlcov/index.html)
[![Tests](https://img.shields.io/badge/tests-74%20passing-brightgreen)](./tests/)
[![Python](https://img.shields.io/badge/python-3.12-blue)](https://python.org)
[![Cloud Functions](https://img.shields.io/badge/Cloud%20Functions-Gen2-blue)](https://cloud.google.com/functions)
[![Terraform](https://img.shields.io/badge/Terraform-1.0+-purple)](https://terraform.io)

A serverless authentication gateway for Bittensor validators, built on Google Cloud Functions. Provides secure JWT and OAuth2 token generation for validator messaging infrastructure.

## ✨ Features

- 🔐 **Bittensor Authentication** - Cryptographic signature verification for validator hotkeys
- 🎫 **JWT Token Generation** - Secure access tokens for API authentication
- ☁️ **OAuth2 Pub/Sub Tokens** - Google Cloud Pub/Sub service account impersonation
- ⚡ **Serverless Architecture** - Cloud Functions Gen2 with auto-scaling and pay-per-request
- 🗄️ **Cloud SQL Storage** - PostgreSQL for all data persistence and rate limiting
- 🏗️ **Infrastructure as Code** - Complete Terraform deployment automation
- 📊 **Comprehensive Testing** - 89% test coverage with local and integration tests
- 🐳 **Local Development** - Docker Compose stack with GCP emulators
- 🔧 **Developer Experience** - Rich Makefile with 50+ automation commands

## 🚀 Quick Start

### Prerequisites
- [Google Cloud SDK](https://cloud.google.com/sdk/docs/install)
- [Terraform](https://terraform.io/downloads) 1.0+
- Python 3.12+ and [uv](https://docs.astral.sh/uv/)
- Docker (for local development)

### Production Deployment (Cloud Functions)
```bash
# 1. Clone and configure
git clone <repository-url>
cd validator-token-gateway

# 2. Setup Google Cloud
gcloud auth login
gcloud config set project YOUR_PROJECT_ID

# 3. Deploy serverless infrastructure
export GCP_PROJECT_ID="your-project-id"
export GCP_REGION="us-central1"
make deploy-function

# 4. Get your function URL
cd terraform && terraform output function_url
```

### Local Development
```bash
# Setup development environment
make setup-dev

# Start local stack (FastAPI + PostgreSQL)
make dev

# Run tests
make test-cov
```

**Local endpoints:**
- **API Server**: http://localhost:8000
- **Interactive Docs**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## 🐳 Docker Development

### Full Stack with Docker
```bash
# Start all services (API + PostgreSQL)
make docker-up

# View logs
make docker-logs

# Stop all services
make docker-down

# Clean up resources
make docker-clean
```

**Services included:**
- **API Server**: FastAPI application for local development (port 8000)
- **PostgreSQL**: Database (port 5432)

> **Note**: In production, the application runs as a Cloud Function with Cloud SQL only.

### Manual Docker Commands
```bash
# Build and start all services
docker compose up -d

# View logs
docker compose logs -f

# Stop all services
docker compose down
```

## ☁️ Local GCP Testing

### GCP Emulators Setup
Test your application locally with GCP emulators (like LocalStack for AWS):

```bash
# Setup complete local GCP environment
make setup-local-gcp

# Start only GCP emulators
make start-gcp-emulators

# Check emulator status
make gcp-status

# Run tests against emulators
make test-local-gcp
```

**Available Emulators:**
- **Pub/Sub Emulator**: localhost:8085
- **Secret Manager Mock**: localhost:6380 (Redis-based)
- **Cloud SQL Mock**: localhost:5433
- **Memorystore Mock**: localhost:6381

### Testing Terraform Deployment Locally
```bash
# Test infrastructure setup without deploying to GCP
make setup-local-gcp
make test-local-gcp

# Verify all services work together
make docker-up
make gcp-status
```

## 🎭 Live Validator Demo

### 🚀 Complete Ecosystem Demo
Experience the full validator ecosystem with **real-time Pub/Sub communication**:

```bash
# 🎯 RECOMMENDED: Local demo (most reliable)
make local-demo

# Alternative: Full automated demo
make demo

# Quick 30-second test
make demo-short
```

**✨ What you'll see:**
- 🔐 **Validator Authentication** - Cryptographic signature verification
- 🎫 **Token Exchange** - JWT and Pub/Sub tokens issued
- 📨 **Real-time Communication** - Bidirectional message exchange
- 🤖 **Realistic Behavior** - Authentic validator simulation
- 📊 **Live Monitoring** - Real-time dashboard

### 🎮 Interactive Demo
```bash
# Terminal 1: Start infrastructure
make local-demo

# Terminal 2: Monitor activity
make monitor-pubsub

# Terminal 3: Start Validator Alpha
make run-validator1

# Terminal 4: Start Validator Beta
make run-validator2
```

**🎭 Demo Features:**
- **2 Realistic Validators** (Alpha: 1500 TAO, Beta: 800 TAO)
- **5+ Message Types** (announcements, consensus, metrics, network updates)
- **Real-time Dashboard** showing all validator activity
- **Bidirectional Communication** - validators receive each other's messages
- **Complete Authentication Flow** from start to finish

### 🔍 Evidence of Success
When running, you'll see validators communicating:
```
📥 [Validator Beta] Received from Validator Alpha (UID:1): validator_online
    Content: Validator Validator Alpha is now online

📥 [Validator Alpha] Received from Validator Beta (UID:2): consensus_vote
    Content: Consensus vote from Validator Beta
```

See **[VALIDATOR_DEMO.md](./VALIDATOR_DEMO.md)** for detailed demo guide.

## 🧪 Testing

### Quick Testing
```bash
# Run all tests with coverage
make test-cov

# Run tests in watch mode
make test-watch

# Run specific test categories
make test-unit          # Unit tests only
make test-routes        # Route tests only
make test-services      # Service tests only
```

### Test Structure
Tests are organized to mirror the application structure:
```
tests/
├── routes/            # Tests for app/routes/
├── services/          # Tests for app/services/
├── utils/             # Test utilities and integration tests
├── test_auth.py       # Tests for app/auth.py
├── test_models.py     # Tests for app/models.py
└── test_*.py          # Tests for app/*.py
```

**Current Status**: 89% coverage with 74 passing tests ✅

## 🔧 Development

### Code Quality
```bash
# Quick quality checks
make quick-check        # Lint + format + test

# Individual checks
make lint              # Check code quality
make format            # Auto-format code
make pre-commit        # Run all pre-commit hooks
```

### Development Workflow
```bash
make dev               # Start development server
# ... make changes ...
make quick-check       # Verify changes
git commit             # Pre-commit hooks run automatically
```

**Code Standards:**
- **Linting & Formatting**: Ruff (replaces Black + isort + flake8)
- **Line Length**: 88 characters
- **Import Sorting**: Automatic
- **Pre-commit Hooks**: Automatic quality checks

## 🗄️ Database

### Database Operations
```bash
# Initialize database tables
make init-db

# Open database shell
make db-shell

# Backup database
make backup-db

# Restore from backup
make restore-db BACKUP=backup_20231201_143022.sql
```

### Database Maintenance
```bash
# Test cleanup functionality locally
make test-cleanup

# Manually trigger cleanup on deployed Cloud Function
make trigger-cleanup ENV=production    # For production
make trigger-cleanup ENV=development   # For development
```

## 🚀 Deployment

### Cloud Function Deployment
The application now deploys as a serverless Cloud Function:

```bash
# Set required environment variables
export GCP_PROJECT_ID="your-project-id"
export GCP_REGION="us-central1"

# Deploy Cloud Function with infrastructure
./scripts/deploy-function.sh

# Or use Makefile
make deploy-function
```

### Infrastructure as Code
```bash
# Initialize Terraform
make tf-init

# Plan infrastructure changes
make tf-plan

# Apply infrastructure (Cloud Function + Cloud SQL + Scheduler)
make tf-apply

# Destroy infrastructure (careful!)
make tf-destroy
```

### Automated Cleanup
The system includes automated daily cleanup of expired tokens:

- **🕐 Schedule**: Daily at 2:00 AM UTC (single job in production)
- **🧹 Cleans**: Expired Pub/Sub tokens, expired JWT tokens, old rate limit records
- **🌍 Scope**: Operates across ALL environments (production + development)
- **🔒 Security**: Uses OIDC authentication with dedicated service account
- **📊 Monitoring**: Returns detailed cleanup statistics

**Infrastructure Components:**
- Single Cloud Scheduler job (runs in production, cleans all environments)
- Dedicated service account with minimal permissions
- Secure OIDC token authentication
- Automatic retry and error handling

### Manual Deployment
```bash
# Package and upload function
cd functions/auth_function
zip -r function-source.zip . -x "*.pyc" "*__pycache__*"
gsutil cp function-source.zip gs://your-bucket/

# Apply Terraform manually
cd ../../terraform
terraform init
terraform apply
```

### Migration from FastAPI
If migrating from the previous FastAPI version:

1. **Run database migration**: `python migrations/add_rate_limiting_table.py`
2. **Deploy Cloud Function**: `./scripts/deploy-function.sh`
3. **Update client URLs**: Point to new Cloud Function URL
4. **Test endpoints**: All existing APIs work the same way

## 🛠️ Utility Scripts

The project includes several utility scripts in `tests/utils/`:

```bash
# Generate authentication signature
python tests/utils/generate_signature.py

# Get development token (dev mode only)
python tests/utils/get_dev_token.py

# Verify Pub/Sub setup
python tests/utils/verify_pubsub_setup.py

# Test Pub/Sub publishing
python tests/utils/test_pubsub.py --token <jwt_token>
```

## 📁 Project Structure

```
validator-token-gateway/
├── app/                    # FastAPI application (for local development)
│   ├── routes/            # API route handlers
│   │   ├── auth.py       # Authentication endpoints
│   │   ├── dev.py        # Development endpoints
│   │   ├── system.py     # System health endpoints
│   │   └── validators.py # Validator info endpoints
│   ├── services/          # Business logic services (shared from core/)
│   │   └── __init__.py            # Imports shared services from core
│   ├── auth.py           # Authentication functions
│   ├── config.py         # Configuration settings
│   ├── db.py             # Database configuration
│   └── models.py         # Data models (SQLAlchemy + Pydantic)
├── functions/             # Cloud Function (production deployment)
│   └── auth_function/    # Serverless authentication function
│       ├── services/     # Adapted services (no Redis)
│       ├── main.py       # Cloud Function entry point
│       ├── config.py     # Function configuration
│       ├── models.py     # Database models
│       └── requirements.txt # Function dependencies
├── migrations/           # Database migrations
├── scripts/              # Deployment and utility scripts
├── tests/                # Test suite (mirrors app structure)
│   ├── routes/          # Route tests
│   ├── services/        # Service tests
│   ├── utils/           # Test utilities and integration tests
│   └── test_*.py        # Module tests
├── terraform/           # Infrastructure as code (Cloud Function + Cloud SQL)
├── Makefile             # Development automation (50+ commands)
├── DEVELOPMENT.md       # Comprehensive development guide
├── QUICK_REFERENCE.md   # Command cheat sheet
├── docker-compose.yml   # Local development stack
├── pyproject.toml       # Python dependencies and config
└── main.py             # FastAPI application entry point (dev only)
```

## 🆘 Troubleshooting

### Common Issues

#### Environment Setup
```bash
# Check environment status
make status

# Clean and reinstall
make clean
make setup-dev
```

#### Docker Issues
```bash
# Clean up Docker resources
make docker-clean

# Rebuild and restart
make docker-build
make docker-up
```

#### Test Issues
```bash
# Clear test cache and run
make clean
make test-cov
```

### Getting Help
```bash
# Show all available commands
make help

# Show project information
make info

# Check environment status
make status
```

## 📦 Package Management

This project uses **uv** for fast, reliable Python package management. uv is a modern replacement for Poetry that offers:

- ⚡ **10-100x faster** dependency resolution and installation
- 🔒 **Reliable** lock file management with `uv.lock`
- 🐍 **Standard** Python packaging with `pyproject.toml`
- 🛠️ **Simple** commands similar to Poetry

### Quick uv Commands
```bash
# Install dependencies
uv sync                    # Install all dependencies
uv sync --no-dev          # Install only production dependencies

# Add/remove packages
uv add package-name        # Add a dependency
uv add --dev package-name  # Add a dev dependency
uv remove package-name     # Remove a dependency

# Run commands
uv run python script.py    # Run Python with project dependencies
uv run pytest             # Run tests
uv run uvicorn main:app    # Start the server
```

## 📚 Documentation

- **[Development Guide](./DEVELOPMENT.md)** - Comprehensive development documentation
- **[Quick Reference](./QUICK_REFERENCE.md)** - Command cheat sheet
- **[API Documentation](http://localhost:8000/docs)** - Interactive API docs (when running)
- **[Test Coverage Report](./htmlcov/index.html)** - Detailed coverage report

## 🤝 Contributing

1. **Setup**: `make setup-dev`
2. **Development**: `make dev`
3. **Testing**: `make test-cov` (ensure 80%+ coverage)
4. **Quality**: `make quick-check` before committing
5. **Commit**: Pre-commit hooks run automatically

## 📊 Project Status

- ✅ **89% Test Coverage** (Target: 80%)
- ✅ **74 Tests Passing** (5 skipped)
- ✅ **Comprehensive Makefile** (50+ commands)
- ✅ **Structured Test Organization**
- ✅ **Complete Documentation**
- ✅ **Docker Ready**
- ✅ **CI/CD Ready**

## 📄 License

[Add your license information here]

---

**Need help?** Run `make help` to see all available commands or check the [Development Guide](./DEVELOPMENT.md) for detailed instructions.
