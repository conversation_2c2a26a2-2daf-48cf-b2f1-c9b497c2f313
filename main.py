"""
FastAPI application for Validator Token Gateway - LOCAL DEVELOPMENT ONLY.

This FastAPI application is used for local development and testing.
In production, the application runs as a Cloud Function (see functions/auth_function/).

This application provides authentication and token generation services
for validators in the Bittensor network.
"""

import os

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from core.config import settings
from core import __version__
from app.db import create_tables
from app.routes import router

app = FastAPI(
    title="Validator Token Gateway",
    description=(
        "A FastAPI gateway for authenticating validators and issuing secure tokens"
        " - LOCAL DEVELOPMENT ONLY"
    ),
    version=__version__,
)

# Create database tables on startup, but only if not in testing mode
if os.environ.get("TESTING") != "True":
    create_tables()

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include all routes
app.include_router(router)
