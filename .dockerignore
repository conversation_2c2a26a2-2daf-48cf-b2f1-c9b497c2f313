# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
.venv/

# Package Management
# Note: uv.lock should be included in Docker builds for reproducible builds

# Terraform
terraform/
.terraform/
*.tfstate
*.tfstate.backup
.terraform.lock.hcl
terraform.tfvars
*.tfvars

# IDE
.idea/
.vscode/

# Docker
.dockerignore
docker-compose.yml

# Other
# README.md - needed for pyproject.toml build
LICENSE