#!/usr/bin/env python3
"""
Pub/Sub Monitor Dashboard

This script monitors all Pub/Sub activity and displays it in real-time.
Shows messages flowing between validators.
"""

import json
import os
import threading
import time
from collections import defaultdict, deque
from datetime import datetime

from google.api_core import exceptions as gcp_exceptions
from google.cloud import pubsub_v1


class PubSubMonitor:
    """Monitors Pub/Sub activity and displays real-time dashboard."""

    def __init__(self, project_id: str = "local-project"):
        self.project_id = project_id
        self.subscriber = pubsub_v1.SubscriberClient()
        self.publisher = pubsub_v1.PublisherClient()

        # Message tracking
        self.message_history: deque = deque(maxlen=100)  # Keep last 100 messages
        self.topic_stats: dict[str, dict] = defaultdict(
            lambda: {"total_messages": 0, "last_message": None, "validators": set()}
        )
        self.validator_stats: dict[str, dict] = defaultdict(
            lambda: {
                "messages_sent": 0,
                "last_seen": None,
                "topics": set(),
                "name": "Unknown",
            }
        )

        # Real-time counters
        self.total_messages_received = 0
        self.start_time = datetime.now()

        self.running = False
        self.listeners: list[threading.Thread] = []

    def setup_monitor_subscriptions(self):
        """Create monitor subscriptions for all topics."""
        # Get all topics
        project_path = f"projects/{self.project_id}"

        try:
            topics = list(self.publisher.list_topics(request={"project": project_path}))

            for topic in topics:
                topic_name = topic.name.split("/")[-1]
                subscription_name = f"monitor-{topic_name}"
                subscription_path = self.subscriber.subscription_path(
                    self.project_id, subscription_name
                )

                try:
                    # Create monitor subscription
                    self.subscriber.create_subscription(
                        request={"name": subscription_path, "topic": topic.name}
                    )
                    print(f"📊 Created monitor subscription for: {topic_name}")
                except gcp_exceptions.AlreadyExists:
                    pass  # Subscription already exists

        except Exception as e:
            print(f"❌ Error setting up monitor subscriptions: {e}")

    def message_callback(self, topic_name: str):
        """Create callback function for a specific topic."""

        def callback(message):
            try:
                # Parse message data
                data = json.loads(message.data.decode("utf-8"))

                # Extract validator info
                validator_uid = data.get("validator_uid", "unknown")
                validator_name = data.get("validator_name", "Unknown")
                message_type = data.get("type", "message")
                timestamp = data.get("timestamp", datetime.now().isoformat())

                # Update statistics
                self.total_messages_received += 1
                self.topic_stats[topic_name]["total_messages"] += 1
                self.topic_stats[topic_name]["last_message"] = timestamp
                self.topic_stats[topic_name]["validators"].add(validator_uid)

                self.validator_stats[validator_uid]["messages_sent"] += 1
                self.validator_stats[validator_uid]["last_seen"] = timestamp
                self.validator_stats[validator_uid]["topics"].add(topic_name)
                self.validator_stats[validator_uid]["name"] = validator_name

                # Add to message history
                message_info = {
                    "timestamp": timestamp,
                    "topic": topic_name,
                    "validator_uid": validator_uid,
                    "validator_name": validator_name,
                    "type": message_type,
                    "content": data.get("content", ""),
                    "data": data,
                }
                self.message_history.append(message_info)

                # Print real-time message
                time_str = datetime.fromisoformat(
                    timestamp.replace("Z", "+00:00")
                ).strftime("%H:%M:%S")
                print(
                    f"[{time_str}] 📨 {topic_name}: {validator_name} (UID:{validator_uid}) -> {message_type}"
                )
                if data.get("content"):
                    print(f"    💬 {data['content']}")

                message.ack()

            except Exception as e:
                print(f"❌ Error processing message: {e}")
                message.ack()

        return callback

    def start_topic_listener(self, topic_name: str):
        """Start listening to a specific topic."""
        subscription_name = f"monitor-{topic_name}"
        subscription_path = self.subscriber.subscription_path(
            self.project_id, subscription_name
        )

        def listen():
            try:
                callback = self.message_callback(topic_name)
                streaming_pull_future = self.subscriber.subscribe(
                    subscription_path, callback=callback
                )

                print(f"👂 Started monitoring: {topic_name}")

                while self.running:
                    try:
                        streaming_pull_future.result(timeout=1.0)
                    except:
                        pass  # Timeout is expected

                streaming_pull_future.cancel()

            except Exception as e:
                print(f"❌ Error listening to {topic_name}: {e}")

        thread = threading.Thread(target=listen, daemon=True)
        thread.start()
        self.listeners.append(thread)

    def display_dashboard(self):
        """Display real-time dashboard."""

        def clear_screen():
            # Clear screen with ANSI escape codes (works on most terminals)
            print("\033[2J\033[H", end="", flush=True)

        update_count = 0

        while self.running:
            clear_screen()
            update_count += 1

            print("=" * 80)
            print("🔍 VALIDATOR TOKEN GATEWAY - PUB/SUB MONITOR DASHBOARD")
            print("=" * 80)
            print(f"📊 Project: {self.project_id}")
            print(
                f"⏰ Last Update: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} (#{update_count})"
            )
            print("🔄 Auto-refresh: Every 1 second")

            # Calculate uptime and message rate
            uptime = datetime.now() - self.start_time
            uptime_str = str(uptime).split(".")[0]  # Remove microseconds
            msg_rate = self.total_messages_received / max(uptime.total_seconds(), 1)

            print(
                f"📈 Total Messages: {self.total_messages_received} | Rate: {msg_rate:.1f}/sec | Uptime: {uptime_str}"
            )
            print()

            # Validator Statistics
            print("👥 ACTIVE VALIDATORS")
            print("-" * 40)
            if self.validator_stats:
                for uid, stats in self.validator_stats.items():
                    last_seen = "Never"
                    if stats["last_seen"]:
                        try:
                            last_seen_dt = datetime.fromisoformat(
                                stats["last_seen"].replace("Z", "+00:00")
                            )
                            last_seen = last_seen_dt.strftime("%H:%M:%S")
                        except:
                            last_seen = "Unknown"

                    print(f"  🤖 {stats['name']} (UID: {uid})")
                    print(f"     📤 Messages: {stats['messages_sent']}")
                    print(f"     ⏰ Last Seen: {last_seen}")
                    print(f"     📋 Topics: {', '.join(stats['topics'])}")
                    print()
            else:
                print("  No validators active yet...")
            print()

            # Topic Statistics
            print("📨 TOPIC ACTIVITY")
            print("-" * 40)
            if self.topic_stats:
                for topic, stats in self.topic_stats.items():
                    last_msg = "Never"
                    if stats["last_message"]:
                        try:
                            last_msg_dt = datetime.fromisoformat(
                                stats["last_message"].replace("Z", "+00:00")
                            )
                            last_msg = last_msg_dt.strftime("%H:%M:%S")
                        except:
                            last_msg = "Unknown"

                    print(f"  📋 {topic}")
                    print(f"     📊 Total Messages: {stats['total_messages']}")
                    print(f"     ⏰ Last Message: {last_msg}")
                    print(f"     👥 Active Validators: {len(stats['validators'])}")
                    print()
            else:
                print("  No topic activity yet...")
            print()

            # Recent Messages
            print("📜 RECENT MESSAGES (Last 10)")
            print("-" * 40)
            recent_messages = list(self.message_history)[-10:]
            if recent_messages:
                for msg in reversed(recent_messages):  # Show newest first
                    try:
                        time_str = datetime.fromisoformat(
                            msg["timestamp"].replace("Z", "+00:00")
                        ).strftime("%H:%M:%S")
                    except:
                        time_str = "??:??:??"

                    print(
                        f"  [{time_str}] {msg['topic']}: {msg['validator_name']} -> {msg['type']}"
                    )
                    if msg.get("content"):
                        content = (
                            msg["content"][:60] + "..."
                            if len(msg["content"]) > 60
                            else msg["content"]
                        )
                        print(f"    💬 {content}")
            else:
                print("  No messages yet...")

            print()
            # Connection Status
            print("🔗 CONNECTION STATUS")
            print("-" * 40)
            try:
                # Test Pub/Sub connection
                project_path = f"projects/{self.project_id}"
                topics = list(
                    self.publisher.list_topics(request={"project": project_path})
                )
                print(f"  ✅ Pub/Sub Emulator: Connected ({len(topics)} topics)")
            except Exception as e:
                print(f"  ❌ Pub/Sub Emulator: Disconnected ({str(e)[:50]}...)")

            print()
            print("=" * 80)
            print("Press Ctrl+C to stop monitoring | Auto-refresh: 1 second")
            print("=" * 80)

            time.sleep(1)  # Update every 1 second for more responsive monitoring

    def start_monitoring(self):
        """Start monitoring all topics."""
        print("🚀 Starting Pub/Sub Monitor...")

        # Setup environment
        if not os.getenv("PUBSUB_EMULATOR_HOST"):
            os.environ["PUBSUB_EMULATOR_HOST"] = "localhost:8085"

        self.running = True

        # Setup monitor subscriptions
        self.setup_monitor_subscriptions()

        # Get all topics and start listeners
        project_path = f"projects/{self.project_id}"

        try:
            topics = list(self.publisher.list_topics(request={"project": project_path}))

            for topic in topics:
                topic_name = topic.name.split("/")[-1]
                self.start_topic_listener(topic_name)

            if not topics:
                print(
                    "⚠️  No topics found. Validators will create them when they start."
                )

        except Exception as e:
            print(f"❌ Error getting topics: {e}")

        # Start dashboard in main thread
        try:
            self.display_dashboard()
        except KeyboardInterrupt:
            print("\n🛑 Stopping monitor...")
            self.running = False

            # Wait for listeners to stop
            for listener in self.listeners:
                listener.join(timeout=1.0)

            print("✅ Monitor stopped.")


def main():
    """Main function."""
    print("🔍 Validator Token Gateway - Pub/Sub Monitor")
    print("This tool monitors all Pub/Sub activity between validators.")
    print()

    # Setup environment for local testing
    os.environ["PUBSUB_EMULATOR_HOST"] = "localhost:8085"
    os.environ["GCP_PROJECT_ID"] = "local-project"

    monitor = PubSubMonitor()
    monitor.start_monitoring()


if __name__ == "__main__":
    main()
