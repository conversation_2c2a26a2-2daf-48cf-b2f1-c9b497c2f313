{"name": "Validator Beta", "hotkey": "5HGjWAeFDfFCWPsjFQdVV2Msvz2XtMktvgocEZcCj68kUMaw", "coldkey": "5DAAnrj7VHTznn2AWBemMuyBwZWs6FNFjdyVXUeYum3PTXFy", "uid": 2, "stake": 800.0, "trust": 0.75, "netuid": 27, "ip": "*************", "port": 8092, "version": 640, "incentive": 0.6, "consensus": 0.7, "validator_permit": true, "last_update": 1000, "active": true, "axon_info": {"version": 640, "ip": "*************", "port": 8092, "ip_type": 4, "hotkey": "5HGjWAeFDfFCWPsjFQdVV2Msvz2XtMktvgocEZcCj68kUMaw", "coldkey": "5DAAnrj7VHTznn2AWBemMuyBwZWs6FNFjdyVXUeYum3PTXFy"}, "prometheus_info": {"version": 640, "ip": "*************", "port": 9092, "ip_type": 4}, "description": "Beta validator for local testing - medium stake, medium trust", "topics": ["validator-announcements", "miner-updates", "performance-metrics"]}