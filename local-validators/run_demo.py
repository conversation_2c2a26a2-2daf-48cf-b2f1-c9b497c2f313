#!/usr/bin/env python3
"""
Validator Ecosystem Demo

This script orchestrates a complete demo of the validator ecosystem:
1. Starts the token gateway service
2. Starts GCP emulators
3. Launches 2 fake validators
4. Shows Pub/Sub monitor dashboard
5. Demonstrates validator-to-validator communication
"""

import os
import signal
import subprocess
import sys
import time


class DemoOrchestrator:
    """Orchestrates the complete validator ecosystem demo."""

    def __init__(self):
        self.processes: list[subprocess.Popen] = []
        self.running = False

    def run_command(
        self, command: str, name: str, wait: bool = False
    ) -> subprocess.Popen | None:
        """Run a command and track the process."""
        print(f"🚀 Starting {name}...")

        try:
            if wait:
                result = subprocess.run(command, shell=True, check=True)
                print(f"✅ {name} completed successfully")
                return None
            else:
                process = subprocess.Popen(
                    command,
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    preexec_fn=os.setsid if os.name != "nt" else None,
                )
                self.processes.append(process)
                print(f"✅ {name} started (PID: {process.pid})")
                return process

        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to start {name}: {e}")
            return None
        except Exception as e:
            print(f"❌ Error starting {name}: {e}")
            return None

    def wait_for_service(self, url: str, timeout: int = 60, name: str = "service"):
        """Wait for a service to become available."""
        import requests

        print(f"⏳ Waiting for {name} to be ready...")
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    print(f"✅ {name} is ready!")
                    return True
            except:
                pass

            time.sleep(2)

        print(f"❌ {name} failed to start within {timeout} seconds")
        return False

    def setup_environment(self):
        """Setup environment variables."""
        os.environ["PUBSUB_EMULATOR_HOST"] = "localhost:8085"
        os.environ["GCP_PROJECT_ID"] = "local-project"
        os.environ["ENVIRONMENT"] = "local-gcp"

        print("🔧 Environment configured for local GCP testing")

    def cleanup(self):
        """Clean up all processes."""
        print("\n🧹 Cleaning up processes...")

        for process in self.processes:
            try:
                if os.name == "nt":
                    process.terminate()
                else:
                    os.killpg(os.getpgid(process.pid), signal.SIGTERM)

                # Wait for process to terminate
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    if os.name == "nt":
                        process.kill()
                    else:
                        os.killpg(os.getpgid(process.pid), signal.SIGKILL)

            except Exception as e:
                print(f"⚠️  Error terminating process {process.pid}: {e}")

        self.processes.clear()
        print("✅ Cleanup completed")

    def run_demo(self, duration: int = 120):
        """Run the complete demo."""
        print("=" * 80)
        print("🎭 VALIDATOR TOKEN GATEWAY ECOSYSTEM DEMO")
        print("=" * 80)
        print(f"Duration: {duration} seconds")
        print("This demo will show:")
        print("  1. Token gateway authentication")
        print("  2. Pub/Sub token generation")
        print("  3. Validator-to-validator communication")
        print("  4. Real-time message monitoring")
        print("=" * 80)
        print()

        try:
            # Setup environment
            self.setup_environment()

            # Step 1: Start GCP emulators
            print("📋 Step 1: Starting GCP emulators...")
            if not self.run_command("make setup-local-gcp", "GCP Emulators", wait=True):
                return False

            # Wait for emulators to be ready
            time.sleep(10)

            # Step 2: Start the token gateway API
            print("\n📋 Step 2: Starting Token Gateway API...")
            api_process = self.run_command("make dev", "Token Gateway API")

            if not self.wait_for_service(
                "http://localhost:8000/health", name="Token Gateway API"
            ):
                return False

            # Step 3: Start Pub/Sub monitor in background
            print("\n📋 Step 3: Starting Pub/Sub Monitor...")
            monitor_process = self.run_command(
                "python local-validators/pubsub_monitor.py", "Pub/Sub Monitor"
            )

            # Give monitor time to setup
            time.sleep(5)

            # Step 4: Start validators
            print("\n📋 Step 4: Starting Validators...")

            # Start Validator 1 (Alpha)
            validator1_process = self.run_command(
                f"python local-validators/validator_simulator.py local-validators/validator1.json --duration {duration}",
                "Validator Alpha",
            )

            # Wait a bit before starting second validator
            time.sleep(3)

            # Start Validator 2 (Beta)
            validator2_process = self.run_command(
                f"python local-validators/validator_simulator.py local-validators/validator2.json --duration {duration}",
                "Validator Beta",
            )

            print("\n🎉 All services started successfully!")
            print("\n" + "=" * 80)
            print("🔍 DEMO STATUS")
            print("=" * 80)
            print("✅ Token Gateway API: http://localhost:8000")
            print("✅ API Documentation: http://localhost:8000/docs")
            print("✅ Pub/Sub Emulator: localhost:8085")
            print("✅ Validator Alpha: Running")
            print("✅ Validator Beta: Running")
            print("✅ Pub/Sub Monitor: Active")
            print("=" * 80)
            print()
            print("🎭 The demo is now running!")
            print("   - Validators are authenticating and getting tokens")
            print("   - Messages are flowing between validators via Pub/Sub")
            print("   - Monitor dashboard shows real-time activity")
            print()
            print(f"⏰ Demo will run for {duration} seconds...")
            print("   Press Ctrl+C to stop early")
            print("=" * 80)

            # Wait for demo duration or user interrupt
            start_time = time.time()
            self.running = True

            while self.running and (time.time() - start_time) < duration:
                time.sleep(1)

            if self.running:
                print(f"\n⏰ Demo completed after {duration} seconds")

        except KeyboardInterrupt:
            print("\n🛑 Demo interrupted by user")
            self.running = False

        except Exception as e:
            print(f"\n❌ Demo error: {e}")

        finally:
            self.cleanup()

            # Clean up Docker resources
            print("\n🧹 Cleaning up Docker resources...")
            self.run_command("make clean-gcp", "Docker Cleanup", wait=True)

            print("\n🎭 Demo completed!")
            print("Thank you for trying the Validator Token Gateway!")


def main():
    """Main function."""
    import argparse

    parser = argparse.ArgumentParser(description="Validator Ecosystem Demo")
    parser.add_argument(
        "--duration", type=int, default=120, help="Demo duration in seconds"
    )

    args = parser.parse_args()

    # Check if we're in the right directory
    if not os.path.exists("local-validators"):
        print("❌ Please run this script from the project root directory")
        print("   The local-validators/ directory should be present")
        sys.exit(1)

    if not os.path.exists("Makefile"):
        print("❌ Makefile not found. Please run from project root directory")
        sys.exit(1)

    orchestrator = DemoOrchestrator()

    # Setup signal handler for clean shutdown
    def signal_handler(signum, frame):
        print(f"\n🛑 Received signal {signum}")
        orchestrator.running = False

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    orchestrator.run_demo(args.duration)


if __name__ == "__main__":
    main()
