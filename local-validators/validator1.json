{"name": "Validator Alpha", "hotkey": "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi", "coldkey": "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY", "uid": 1, "stake": 1500.0, "trust": 0.95, "netuid": 27, "ip": "*************", "port": 8091, "version": 640, "incentive": 0.8, "consensus": 0.9, "validator_permit": true, "last_update": 1000, "active": true, "axon_info": {"version": 640, "ip": "*************", "port": 8091, "ip_type": 4, "hotkey": "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi", "coldkey": "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY"}, "prometheus_info": {"version": 640, "ip": "*************", "port": 9091, "ip_type": 4}, "description": "Alpha validator for local testing - high stake, high trust", "topics": ["validator-announcements", "network-updates", "consensus-messages"]}