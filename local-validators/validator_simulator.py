#!/usr/bin/env python3
"""
Validator Simulator for Local Testing

This script simulates a Bittensor validator that:
1. Authenticates with the token gateway
2. Gets a Pub/Sub token
3. Publishes and subscribes to messages
4. Demonstrates validator-to-validator communication
"""

import argparse
import asyncio
import json
import os
import random
import sys
import time
from datetime import datetime

import requests
from google.api_core import exceptions as gcp_exceptions
from google.cloud import pubsub_v1

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class ValidatorSimulator:
    """Simulates a Bittensor validator for local testing."""

    def __init__(self, config_file: str, gateway_url: str = "http://localhost:8000"):
        """Initialize validator simulator."""
        self.gateway_url = gateway_url
        self.config = self._load_config(config_file)
        self.jwt_token: str | None = None
        self.pubsub_token: str | None = None
        self.publisher: pubsub_v1.PublisherClient | None = None
        self.subscriber: pubsub_v1.SubscriberClient | None = None
        self.project_id = os.getenv("GCP_PROJECT_ID", "local-project")

        # Setup Pub/Sub emulator
        if os.getenv("PUBSUB_EMULATOR_HOST"):
            print(f"🔧 Using Pub/Sub emulator: {os.getenv('PUBSUB_EMULATOR_HOST')}")

        self.running = False

    def _load_config(self, config_file: str) -> dict:
        """Load validator configuration."""
        try:
            with open(config_file, "r") as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"❌ Config file not found: {config_file}")
            sys.exit(1)
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON in config file: {e}")
            sys.exit(1)

    def _generate_signature(self, message: str) -> str:
        """Generate a mock signature for authentication."""
        # In real implementation, this would use the validator's private key
        # For simulation, we'll create a predictable signature
        import hashlib
        import hmac

        # Use hotkey as secret for consistent signatures
        secret = self.config["hotkey"].encode()
        signature = hmac.new(secret, message.encode(), hashlib.sha256).hexdigest()
        return f"0x{signature}"

    async def authenticate(self) -> bool:
        """Authenticate with the token gateway."""
        print(f"🔐 [{self.config['name']}] Authenticating with gateway...")

        # Create authentication message
        timestamp = int(time.time())
        message = f"Authenticate to Bittensor Subnet 27:{timestamp}"
        signature = self._generate_signature(message)

        auth_data = {
            "hotkey": self.config["hotkey"],
            "signature": signature,
            "message": message,
            "timestamp": timestamp,
        }

        try:
            response = requests.post(
                f"{self.gateway_url}/auth/dev-token",
                headers={"Content-Type": "application/json"},
            )

            if response.status_code == 200:
                data = response.json()
                self.jwt_token = data["access_token"]
                print(f"✅ [{self.config['name']}] Authentication successful!")
                print(
                    f"   Token expires in: {data.get('expires_in', 'unknown')} seconds"
                )
                return True
            else:
                print(
                    f"❌ [{self.config['name']}] Authentication failed: {response.text}"
                )
                return False

        except requests.RequestException as e:
            print(
                f"❌ [{self.config['name']}] Network error during authentication: {e}"
            )
            return False

    async def get_pubsub_token(self) -> bool:
        """Get Pub/Sub token from the gateway."""
        if not self.jwt_token:
            print(f"❌ [{self.config['name']}] No JWT token available")
            return False

        print(f"📨 [{self.config['name']}] Getting Pub/Sub token...")

        try:
            response = requests.post(
                f"{self.gateway_url}/auth/dev-pubsub-token",
                headers={"Authorization": f"Bearer {self.jwt_token}"},
            )

            if response.status_code == 200:
                data = response.json()
                self.pubsub_token = data["access_token"]
                print(f"✅ [{self.config['name']}] Pub/Sub token received!")
                if "topic_paths" in data:
                    print("   Available Topics:")
                    for topic_type, path in data["topic_paths"].items():
                        print(f"     {topic_type}: {path}")
                else:
                    print(f"   Topic: {data.get('topic_path', 'unknown')}")
                print(f"   Scope: {data.get('scope', 'unknown')}")
                return True
            else:
                print(
                    f"❌ [{self.config['name']}] Failed to get Pub/Sub token: {response.text}"
                )
                return False

        except requests.RequestException as e:
            print(
                f"❌ [{self.config['name']}] Network error getting Pub/Sub token: {e}"
            )
            return False

    def setup_pubsub_clients(self):
        """Setup Pub/Sub publisher and subscriber clients."""
        try:
            self.publisher = pubsub_v1.PublisherClient()
            self.subscriber = pubsub_v1.SubscriberClient()
            print(f"📡 [{self.config['name']}] Pub/Sub clients initialized")
        except Exception as e:
            print(f"❌ [{self.config['name']}] Failed to setup Pub/Sub clients: {e}")

    def create_topics_and_subscriptions(self):
        """Create necessary topics and subscriptions."""
        topics_to_create = [
            "validator-announcements",
            "network-updates",
            "consensus-messages",
            "miner-updates",
            "performance-metrics",
        ]

        for topic_name in topics_to_create:
            topic_path = self.publisher.topic_path(self.project_id, topic_name)
            subscription_path = self.subscriber.subscription_path(
                self.project_id, f"{topic_name}-{self.config['uid']}"
            )

            try:
                # Create topic
                self.publisher.create_topic(request={"name": topic_path})
                print(f"📝 [{self.config['name']}] Created topic: {topic_name}")
            except gcp_exceptions.AlreadyExists:
                pass  # Topic already exists

            try:
                # Create subscription
                self.subscriber.create_subscription(
                    request={"name": subscription_path, "topic": topic_path}
                )
                print(
                    f"📬 [{self.config['name']}] Created subscription: {topic_name}-{self.config['uid']}"
                )
            except gcp_exceptions.AlreadyExists:
                pass  # Subscription already exists

    async def publish_message(self, topic_name: str, message_data: dict):
        """Publish a message to a topic."""
        topic_path = self.publisher.topic_path(self.project_id, topic_name)

        # Add validator info to message
        message_data.update(
            {
                "validator_uid": self.config["uid"],
                "validator_name": self.config["name"],
                "validator_hotkey": self.config["hotkey"],
                "timestamp": datetime.now().isoformat(),
                "stake": self.config["stake"],
                "trust": self.config["trust"],
            }
        )

        message_json = json.dumps(message_data)

        try:
            future = self.publisher.publish(topic_path, message_json.encode("utf-8"))
            message_id = future.result()
            print(
                f"📤 [{self.config['name']}] Published to {topic_name}: {message_data.get('type', 'message')} (ID: {message_id})"
            )
        except Exception as e:
            print(f"❌ [{self.config['name']}] Failed to publish message: {e}")

    def listen_to_topic(self, topic_name: str, duration: int = 30):
        """Listen to messages on a topic."""
        subscription_path = self.subscriber.subscription_path(
            self.project_id, f"{topic_name}-{self.config['uid']}"
        )

        def callback(message):
            try:
                data = json.loads(message.data.decode("utf-8"))
                sender_name = data.get("validator_name", "Unknown")
                sender_uid = data.get("validator_uid", "?")
                message_type = data.get("type", "message")

                # Don't show our own messages
                if sender_uid != self.config["uid"]:
                    print(
                        f"📥 [{self.config['name']}] Received from {sender_name} (UID:{sender_uid}): {message_type}"
                    )
                    if "content" in data:
                        print(f"    Content: {data['content']}")

                message.ack()
            except Exception as e:
                print(f"❌ [{self.config['name']}] Error processing message: {e}")
                message.ack()

        try:
            streaming_pull_future = self.subscriber.subscribe(
                subscription_path, callback=callback
            )
            print(f"👂 [{self.config['name']}] Listening to {topic_name}...")

            # Listen for specified duration
            try:
                streaming_pull_future.result(timeout=duration)
            except:
                streaming_pull_future.cancel()

        except Exception as e:
            print(f"❌ [{self.config['name']}] Error listening to topic: {e}")

    async def simulate_validator_behavior(self, duration: int = 60):
        """Simulate typical validator behavior."""
        self.running = True
        start_time = time.time()

        print(
            f"🤖 [{self.config['name']}] Starting validator simulation for {duration} seconds..."
        )

        # Give listeners time to start up
        await asyncio.sleep(2)

        # Announce validator startup
        await self.publish_message(
            "validator-announcements",
            {
                "type": "validator_online",
                "content": f"Validator {self.config['name']} is now online",
                "ip": self.config["ip"],
                "port": self.config["port"],
            },
        )

        message_count = 0

        while self.running and (time.time() - start_time) < duration:
            # Randomly publish different types of messages
            message_types = [
                (
                    "network-updates",
                    {
                        "type": "network_status",
                        "content": f"Network status update from {self.config['name']}",
                        "block_height": random.randint(1000000, 1001000),
                        "peer_count": random.randint(50, 200),
                    },
                ),
                (
                    "consensus-messages",
                    {
                        "type": "consensus_vote",
                        "content": f"Consensus vote from {self.config['name']}",
                        "proposal_id": f"prop_{random.randint(1000, 9999)}",
                        "vote": random.choice(["yes", "no", "abstain"]),
                    },
                ),
                (
                    "performance-metrics",
                    {
                        "type": "performance_report",
                        "content": f"Performance metrics from {self.config['name']}",
                        "cpu_usage": round(random.uniform(10, 80), 2),
                        "memory_usage": round(random.uniform(20, 90), 2),
                        "response_time": round(random.uniform(0.1, 2.0), 3),
                    },
                ),
            ]

            # Publish a random message type
            topic, message = random.choice(message_types)
            await self.publish_message(topic, message)
            message_count += 1

            # Wait between messages
            await asyncio.sleep(random.uniform(5, 15))

        # Announce validator shutdown
        await self.publish_message(
            "validator-announcements",
            {
                "type": "validator_offline",
                "content": f"Validator {self.config['name']} is going offline",
                "messages_sent": message_count,
                "uptime": int(time.time() - start_time),
            },
        )

        print(
            f"🛑 [{self.config['name']}] Simulation completed. Sent {message_count} messages."
        )

    async def run(self, duration: int = 60):
        """Run the complete validator simulation."""
        print(f"🚀 Starting {self.config['name']} (UID: {self.config['uid']})")
        print(f"   Hotkey: {self.config['hotkey'][:20]}...")
        print(f"   Stake: {self.config['stake']} TAO")
        print(f"   Trust: {self.config['trust']}")

        # Step 1: Authenticate
        if not await self.authenticate():
            return False

        # Step 2: Get Pub/Sub token
        if not await self.get_pubsub_token():
            return False

        # Step 3: Setup Pub/Sub clients
        self.setup_pubsub_clients()

        # Step 4: Create topics and subscriptions
        self.create_topics_and_subscriptions()

        # Step 5: Start listening to topics in background
        topics_to_listen = self.config.get(
            "topics",
            ["validator-announcements", "network-updates", "consensus-messages"],
        )

        # Start listening threads for each topic
        import threading

        listener_threads = []

        for topic in topics_to_listen:
            thread = threading.Thread(
                target=self.listen_to_topic,
                args=(topic, duration + 10),  # Listen a bit longer than simulation
                daemon=True,
            )
            thread.start()
            listener_threads.append(thread)

        print(
            f"👂 [{self.config['name']}] Started listening to {len(topics_to_listen)} topics"
        )

        # Step 6: Start validator behavior simulation
        await self.simulate_validator_behavior(duration)

        # Wait a bit for any final messages
        await asyncio.sleep(2)

        print(f"🔇 [{self.config['name']}] Stopping listeners...")

        return True


async def main():
    """Main function to run validator simulation."""
    parser = argparse.ArgumentParser(description="Validator Simulator")
    parser.add_argument("config", help="Path to validator config JSON file")
    parser.add_argument(
        "--duration", type=int, default=60, help="Simulation duration in seconds"
    )
    parser.add_argument(
        "--gateway", default="http://localhost:8000", help="Gateway URL"
    )

    args = parser.parse_args()

    # Setup environment for local testing
    os.environ["PUBSUB_EMULATOR_HOST"] = "localhost:8085"
    os.environ["GCP_PROJECT_ID"] = "local-project"

    simulator = ValidatorSimulator(args.config, args.gateway)

    try:
        await simulator.run(args.duration)
    except KeyboardInterrupt:
        print(f"\n🛑 [{simulator.config['name']}] Simulation interrupted by user")
        simulator.running = False
    except Exception as e:
        print(f"❌ [{simulator.config['name']}] Simulation error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
