# Makefile for Validator Token Gateway
# Serverless Cloud Function gateway for authenticating validators and issuing secure tokens
# Includes local development environment and comprehensive testing suite

.PHONY: help install install-dev test test-cov test-watch lint format clean run dev docker-up docker-down docker-logs docker-clean init-db check-deps security-check pre-commit setup-dev setup-local-gcp gcp-status test-pubsub-emulator clean-gcp demo demo-short demo-long run-validator1 run-validator2 run-validator1-short run-validator2-short monitor-pubsub setup-validators local-demo simple-demo test-validator-communication deploy-function deploy-function-terraform migrate-db cleanup-project

# Default target
.DEFAULT_GOAL := help

# Colors for output
BLUE := \033[36m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
RESET := \033[0m

# Project variables
PROJECT_NAME := validator-token-gateway
PYTHON_VERSION := 3.12
COVERAGE_TARGET := 80

help: ## Show this help message
	@echo "$(BLUE)$(PROJECT_NAME) - Development Commands$(RESET)"
	@echo ""
	@echo "$(GREEN)Setup Commands:$(RESET)"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep -E "(install|setup)" | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(BLUE)%-20s$(RESET) %s\n", $$1, $$2}'
	@echo ""
	@echo "$(GREEN)Development Commands:$(RESET)"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep -E "(run|dev|test|lint|format)" | grep -v -E "(validator|demo|gcp)" | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(BLUE)%-20s$(RESET) %s\n", $$1, $$2}'
	@echo ""
	@echo "$(GREEN)🎭 Validator Demo Commands:$(RESET)"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep -E "(demo|validator|monitor)" | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(BLUE)%-20s$(RESET) %s\n", $$1, $$2}'
	@echo ""
	@echo "$(GREEN)☁️ Local GCP Testing:$(RESET)"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep -E "gcp|pubsub" | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(BLUE)%-20s$(RESET) %s\n", $$1, $$2}'
	@echo ""
	@echo "$(GREEN)Docker Commands:$(RESET)"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep -E "docker" | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(BLUE)%-20s$(RESET) %s\n", $$1, $$2}'
	@echo ""
	@echo "$(GREEN)Maintenance Commands:$(RESET)"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep -E "(clean|check|security|init)" | grep -v -E "(gcp|demo)" | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(BLUE)%-20s$(RESET) %s\n", $$1, $$2}'

# Setup and Installation
install: ## Install production dependencies
	@echo "$(BLUE)Installing production dependencies...$(RESET)"
	uv sync --no-dev

install-dev: ## Install all dependencies including dev tools
	@echo "$(BLUE)Installing all dependencies...$(RESET)"
	uv sync --extra dev

setup-dev: install-dev ## Complete development environment setup
	@echo "$(BLUE)Setting up development environment...$(RESET)"
	uv run pre-commit install
	@echo "$(GREEN)✓ Development environment ready!$(RESET)"
	@echo "$(YELLOW)Run 'make help' to see available commands$(RESET)"

# Testing
test: ## Run all tests
	@echo "$(BLUE)Running tests...$(RESET)"
	uv run pytest -v

test-cov: ## Run tests with coverage report
	@echo "$(BLUE)Running tests with coverage...$(RESET)"
	uv run pytest --cov=app --cov-report=term-missing --cov-report=html
	@echo "$(GREEN)✓ Coverage report generated in htmlcov/$(RESET)"

test-watch: ## Run tests in watch mode
	@echo "$(BLUE)Running tests in watch mode...$(RESET)"
	uv run pytest --cov=app --cov-report=term-missing -f

test-unit: ## Run only unit tests (excluding integration tests)
	@echo "$(BLUE)Running unit tests...$(RESET)"
	uv run pytest tests/ -v --ignore=tests/utils/

test-integration: ## Run only integration tests
	@echo "$(BLUE)Running integration tests...$(RESET)"
	uv run pytest tests/utils/ -v

test-routes: ## Run only route tests
	@echo "$(BLUE)Running route tests...$(RESET)"
	uv run pytest tests/routes/ -v

test-services: ## Run only service tests
	@echo "$(BLUE)Running service tests...$(RESET)"
	uv run pytest tests/services/ -v

# Code Quality
lint: ## Run linting with ruff
	@echo "$(BLUE)Running linter...$(RESET)"
	uv run ruff check .

lint-fix: ## Run linting with auto-fix
	@echo "$(BLUE)Running linter with auto-fix...$(RESET)"
	uv run ruff check . --fix

format: ## Format code with ruff
	@echo "$(BLUE)Formatting code...$(RESET)"
	uv run ruff format .

format-check: ## Check code formatting without making changes
	@echo "$(BLUE)Checking code formatting...$(RESET)"
	uv run ruff format . --check

pre-commit: ## Run pre-commit hooks on all files
	@echo "$(BLUE)Running pre-commit hooks...$(RESET)"
	uv run pre-commit run --all-files

# Development Server
run: ## Run the FastAPI server locally
	@echo "$(BLUE)Starting FastAPI server...$(RESET)"
	uv run uvicorn main:app --reload --host 0.0.0.0 --port 8000

dev: ## Run development server with auto-reload
	@echo "$(BLUE)Starting development server...$(RESET)"
	@echo "$(YELLOW)Server will be available at http://localhost:8000$(RESET)"
	@echo "$(YELLOW)API docs at http://localhost:8000/docs$(RESET)"
	uv run uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Docker Commands
docker-up: ## Start all services with Docker Compose
	@echo "$(BLUE)Starting Docker services...$(RESET)"
	docker-compose up -d
	@echo "$(GREEN)✓ Services started. API available at http://localhost:8000$(RESET)"

docker-down: ## Stop all Docker services
	@echo "$(BLUE)Stopping Docker services...$(RESET)"
	docker-compose down

docker-logs: ## Show Docker logs
	@echo "$(BLUE)Showing Docker logs...$(RESET)"
	docker-compose logs -f

docker-logs-api: ## Show API service logs only
	@echo "$(BLUE)Showing API logs...$(RESET)"
	docker-compose logs -f api

docker-build: ## Build Docker images
	@echo "$(BLUE)Building Docker images...$(RESET)"
	docker-compose build

docker-clean: ## Clean up Docker resources
	@echo "$(BLUE)Cleaning up Docker resources...$(RESET)"
	docker-compose down -v
	docker system prune -f

docker-shell: ## Open shell in API container
	@echo "$(BLUE)Opening shell in API container...$(RESET)"
	docker-compose exec api bash

# Database
init-db: ## Initialize database tables
	@echo "$(BLUE)Initializing database...$(RESET)"
	uv run python init_db.py

db-shell: ## Open database shell
	@echo "$(BLUE)Opening database shell...$(RESET)"
	docker-compose exec db psql -U postgres -d validator_gateway

# Maintenance
clean: ## Clean up cache files and build artifacts
	@echo "$(BLUE)Cleaning up...$(RESET)"
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .coverage htmlcov/ .pytest_cache/
	@echo "$(GREEN)✓ Cleanup complete$(RESET)"

clean-temp: ## Clean up temporary deployment files
	@echo "$(BLUE)Cleaning up temporary files...$(RESET)"
	rm -rf /tmp/function-package
	rm -f terraform/function-source.zip
	@echo "$(GREEN)✓ Temporary files cleaned$(RESET)"

check-deps: ## Check for dependency vulnerabilities
	@echo "$(BLUE)Checking dependencies...$(RESET)"
	uv tree
	@echo "$(YELLOW)Use 'uv add --upgrade <package>' to update specific packages$(RESET)"

security-check: ## Run security checks
	@echo "$(BLUE)Running security checks...$(RESET)"
	uv run pip-audit || echo "$(YELLOW)Install pip-audit for security scanning: uv add --dev pip-audit$(RESET)"

# Utility Commands
shell: ## Open Python shell with project context
	@echo "$(BLUE)Opening Python shell...$(RESET)"
	uv run python

requirements: ## Export requirements.txt
	@echo "$(BLUE)Exporting requirements...$(RESET)"
	uv export --no-hashes --no-dev > requirements.txt

requirements-dev: ## Export dev requirements.txt
	@echo "$(BLUE)Exporting dev requirements...$(RESET)"
	uv export --no-hashes > requirements-dev.txt

# CI/CD helpers
ci-test: ## Run tests for CI environment
	@echo "$(BLUE)Running CI tests...$(RESET)"
	uv run pytest --cov=app --cov-report=xml --cov-fail-under=$(COVERAGE_TARGET)

ci-lint: ## Run linting for CI environment
	@echo "$(BLUE)Running CI linting...$(RESET)"
	uv run ruff check . --output-format=github

# Quick development workflow
quick-check: lint format-check test ## Run quick checks (lint, format, test)
	@echo "$(GREEN)✓ All quick checks passed!$(RESET)"

full-check: clean install-dev lint format-check test-cov security-check ## Run comprehensive checks
	@echo "$(GREEN)✓ All comprehensive checks passed!$(RESET)"

# Project info
info: ## Show project information
	@echo "$(BLUE)Project Information:$(RESET)"
	@echo "  Name: $(PROJECT_NAME)"
	@echo "  Python: $(PYTHON_VERSION)"
	@echo "  Coverage Target: $(COVERAGE_TARGET)%"
	@echo ""
	@echo "$(BLUE)Dependencies:$(RESET)"
	@uv tree | head -20
	@echo ""
	@echo "$(BLUE)Project Structure:$(RESET)"
	@tree -I '__pycache__|*.pyc|.git|htmlcov|.pytest_cache' -L 2 || ls -la

status: ## Show development environment status
	@echo "$(BLUE)Development Environment Status:$(RESET)"
	@echo -n "uv: "
	@uv --version 2>/dev/null && echo "$(GREEN)✓$(RESET)" || echo "$(RED)✗ Not installed$(RESET)"
	@echo -n "Python: "
	@python --version 2>/dev/null && echo "$(GREEN)✓$(RESET)" || echo "$(RED)✗ Not found$(RESET)"
	@echo -n "Docker: "
	@docker --version 2>/dev/null && echo "$(GREEN)✓$(RESET)" || echo "$(RED)✗ Not installed$(RESET)"
	@echo -n "Docker Compose: "
	@docker-compose --version 2>/dev/null && echo "$(GREEN)✓$(RESET)" || echo "$(RED)✗ Not installed$(RESET)"
	@echo ""
	@echo "$(BLUE)Services Status:$(RESET)"
	@docker-compose ps 2>/dev/null || echo "$(YELLOW)Docker services not running$(RESET)"

# Terraform commands
prepare-source: ## Prepare Cloud Function source code for packaging
	@echo "$(BLUE)Preparing function source code...$(RESET)"
	@chmod +x scripts/prepare_function_source.sh
	@./scripts/prepare_function_source.sh

tf-init: ## Initialize Terraform
	@echo "$(BLUE)Initializing Terraform...$(RESET)"
	cd terraform && terraform init

tf-plan: prepare-source ## Show Terraform plan
	@echo "$(BLUE)Showing Terraform plan...$(RESET)"
	cd terraform && terraform plan

tf-apply: prepare-source ## Apply Terraform configuration
	@echo "$(BLUE)Applying Terraform configuration...$(RESET)"
	cd terraform && terraform apply

tf-destroy: ## Destroy Terraform infrastructure
	@echo "$(BLUE)Destroying Terraform infrastructure...$(RESET)"
	cd terraform && terraform destroy

tf-plan-prod: prepare-source ## Show Terraform plan
	@echo "$(BLUE)Showing Terraform plan...$(RESET)"
	cd terraform && terraform plan -var-file="prod.tfvars"

tf-apply-prod: prepare-source ## Apply Terraform configuration
	@echo "$(BLUE)Applying Terraform configuration...$(RESET)"
	cd terraform && terraform apply -var-file="prod.tfvars"

tf-destroy-prod: ## Destroy Terraform infrastructure
	@echo "$(BLUE)Destroying Terraform infrastructure...$(RESET)"
	cd terraform && terraform destroy -var-file="prod.tfvars"

# Cloud Function Deployment
deploy-function: ## Deploy Cloud Function with infrastructure
	@echo "$(BLUE)Deploying Cloud Function...$(RESET)"
	chmod +x scripts/deploy-function.sh
	./scripts/deploy-function.sh

deploy-function-first-time: ## Deploy Cloud Function for first time (skips migration)
	@echo "$(BLUE)Deploying Cloud Function (first time)...$(RESET)"
	chmod +x scripts/deploy-function.sh
	./scripts/deploy-function.sh first-time

deploy-production-only: ## Deploy only production Cloud Function
	@echo "$(BLUE)Deploying production Cloud Function only...$(RESET)"
	cd terraform && terraform apply -var="deploy_development=false" -auto-approve

deploy-development-only: ## Deploy only development Cloud Function
	@echo "$(BLUE)Deploying development Cloud Function only...$(RESET)"
	cd terraform && terraform apply -var="deploy_production=false" -auto-approve

deploy-both-environments: ## Deploy both production and development Cloud Functions
	@echo "$(BLUE)Deploying both environments...$(RESET)"
	cd terraform && terraform apply -auto-approve

# Environment Consistency
check-consistency: ## Check for environment consistency issues
	@echo "$(BLUE)Checking environment consistency...$(RESET)"
	@echo "$(YELLOW)Checking for duplicated service files...$(RESET)"
	@if [ -d "app/services" ] && [ -d "functions/auth_function/services" ]; then \
		echo "$(RED)❌ Found duplicated services - migration needed$(RESET)"; \
		echo "Run 'make migrate-to-core' to fix this"; \
	else \
		echo "$(GREEN)✅ No service duplication found$(RESET)"; \
	fi

migrate-to-core: ## Migrate existing code to use core library
	@echo "$(BLUE)Migrating to core library...$(RESET)"
	@echo "$(YELLOW)⚠️  This will update import statements$(RESET)"
	@echo "$(YELLOW)Creating backup...$(RESET)"
	@cp -r app app_backup_$(shell date +%Y%m%d_%H%M%S) || true
	@cp -r functions functions_backup_$(shell date +%Y%m%d_%H%M%S) || true
	@echo "$(BLUE)Updating imports in app/...$(RESET)"
	@find app -name "*.py" -type f -exec sed -i.bak 's/from app\.services/from core.services/g' {} \; || true
	@find app -name "*.py" -type f -exec sed -i.bak 's/from app\.models/from core.models/g' {} \; || true
	@find app -name "*.py" -type f -exec sed -i.bak 's/from app\.config/from core.config/g' {} \; || true
	@echo "$(BLUE)Updating imports in functions/...$(RESET)"
	@find functions -name "*.py" -type f -exec sed -i.bak 's/from services\./from core.services./g' {} \; || true
	@find functions -name "*.py" -type f -exec sed -i.bak 's/from models/from core.models/g' {} \; || true
	@find functions -name "*.py" -type f -exec sed -i.bak 's/from config/from core.config/g' {} \; || true
	@echo "$(BLUE)Updating imports in tests/...$(RESET)"
	@find tests -name "*.py" -type f -exec sed -i.bak 's/from app\.services/from core.services/g' {} \; || true
	@find tests -name "*.py" -type f -exec sed -i.bak 's/from app\.models/from core.models/g' {} \; || true
	@find tests -name "*.py" -type f -exec sed -i.bak 's/from app\.config/from core.config/g' {} \; || true
	@echo "$(GREEN)✅ Migration complete$(RESET)"
	@echo "$(YELLOW)💡 Run 'make test' to verify everything works$(RESET)"
	@echo "$(YELLOW)💡 Run 'make clean-migration' to remove backup files$(RESET)"

clean-migration: ## Clean up migration backup files
	@echo "$(BLUE)Cleaning up migration backups...$(RESET)"
	@find . -name "*.py.bak" -delete
	@rm -rf app_backup_* functions_backup_* 2>/dev/null || true
	@echo "$(GREEN)✅ Cleanup complete$(RESET)"

test-consistency: ## Run consistency tests between environments
	@echo "$(BLUE)Running environment consistency tests...$(RESET)"
	@echo "$(YELLOW)Testing core library imports...$(RESET)"
	@python -c "from core.services import TokenService, AuthService, BittensorService; print('✅ Core imports working')"
	@echo "$(YELLOW)Testing configuration consistency...$(RESET)"
	@python -c "from core.config import settings; print(f'✅ Environment: {settings.ENVIRONMENT}')"
	@echo "$(YELLOW)Testing model consistency...$(RESET)"
	@python -c "from core.models import Neuron, Token, NeuronModel; print('✅ Models working')"
	@echo "$(GREEN)✅ Consistency tests passed$(RESET)"

verify-no-duplication: ## Verify no code duplication exists
	@echo "$(BLUE)Verifying no code duplication...$(RESET)"
	@if [ -f "app/services/token_service.py" ] || [ -f "functions/auth_function/services/token_service.py" ]; then \
		echo "$(RED)❌ Found duplicated service files$(RESET)"; \
		echo "$(YELLOW)Run 'make migrate-to-core' to fix this$(RESET)"; \
		exit 1; \
	else \
		echo "$(GREEN)✅ No service duplication found$(RESET)"; \
	fi

deploy-function-terraform: ## Deploy Cloud Function using Terraform only
	@echo "$(BLUE)Deploying Cloud Function with Terraform...$(RESET)"
	cd terraform && terraform init && terraform apply -auto-approve

test-packaging: ## Test Cloud Function packaging (verify core module inclusion)
	@echo "$(BLUE)Testing Cloud Function packaging...$(RESET)"
	chmod +x scripts/test-packaging.sh
	./scripts/test-packaging.sh

validate-environment: ## Validate Bittensor network settings align with environment
	@echo "$(BLUE)Validating environment configuration...$(RESET)"
	chmod +x scripts/validate-environment.sh
	./scripts/validate-environment.sh

check-gcp-apis: ## Check if required GCP APIs are enabled
	@echo "$(BLUE)Checking GCP APIs...$(RESET)"
	chmod +x scripts/check-gcp-apis.sh
	./scripts/check-gcp-apis.sh

check-gcp-apis-prod: ## Check if required GCP APIs are enabled
	@echo "$(BLUE)Checking GCP APIs...$(RESET)"
	chmod +x scripts/check-gcp-apis-prod.sh
	./scripts/check-gcp-apis-prod.sh

migrate-db: ## Run database migration to create all tables
	@echo "$(BLUE)Running database migration...$(RESET)"
	uv run python migrations/create_all_tables.py

cleanup-project: ## Clean up project files and refresh documentation
	@echo "$(BLUE)Cleaning up project...$(RESET)"
	chmod +x scripts/cleanup-project.sh
	./scripts/cleanup-project.sh

test-cleanup: ## Test the cleanup functionality locally
	@echo "$(BLUE)Testing cleanup functionality...$(RESET)"
	chmod +x scripts/test-cleanup.py
	uv run python scripts/test-cleanup.py

trigger-cleanup: ## Manually trigger cleanup on deployed Cloud Function (usage: make trigger-cleanup ENV=production)
	@echo "$(BLUE)Triggering cleanup on Cloud Function...$(RESET)"
	chmod +x scripts/trigger-cleanup.sh
	./scripts/trigger-cleanup.sh $(ENV)

# Backup and restore
backup-db: ## Backup database
	@echo "$(BLUE)Backing up database...$(RESET)"
	docker-compose exec db pg_dump -U postgres validator_gateway > backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "$(GREEN)✓ Database backup created$(RESET)"

restore-db: ## Restore database from backup (usage: make restore-db BACKUP=backup_file.sql)
	@echo "$(BLUE)Restoring database from $(BACKUP)...$(RESET)"
	@if [ -z "$(BACKUP)" ]; then echo "$(RED)Error: Please specify BACKUP=filename$(RESET)"; exit 1; fi
	docker-compose exec -T db psql -U postgres validator_gateway < $(BACKUP)
	@echo "$(GREEN)✓ Database restored$(RESET)"

# Local GCP Testing
setup-local-gcp: ## Setup local GCP emulators for testing
	@echo "$(BLUE)Setting up local GCP environment...$(RESET)"
	chmod +x scripts/setup-local-gcp.sh
	./scripts/setup-local-gcp.sh

start-gcp-emulators: ## Start GCP emulators only
	@echo "$(BLUE)Starting GCP emulators...$(RESET)"
	docker-compose -f docker-compose.yml -f docker-compose.local-gcp.yml up -d pubsub-emulator secret-manager-mock
	@echo "$(GREEN)✓ GCP emulators started$(RESET)"

stop-gcp-emulators: ## Stop GCP emulators
	@echo "$(BLUE)Stopping GCP emulators...$(RESET)"
	docker-compose -f docker-compose.yml -f docker-compose.local-gcp.yml down
	@echo "$(GREEN)✓ GCP emulators stopped$(RESET)"

test-local-gcp: ## Run tests against local GCP emulators
	@echo "$(BLUE)Running tests against local GCP emulators...$(RESET)"
	PUBSUB_EMULATOR_HOST=localhost:8085 GCP_PROJECT_ID=local-project uv run pytest tests/ -v -k "pubsub or gcp"

logs-gcp: ## Show GCP emulator logs
	@echo "$(BLUE)Showing GCP emulator logs...$(RESET)"
	docker-compose -f docker-compose.yml -f docker-compose.local-gcp.yml logs -f pubsub-emulator secret-manager-mock

clean-gcp: ## Clean up GCP emulator resources
	@echo "$(BLUE)Cleaning up GCP emulator resources...$(RESET)"
	docker-compose -f docker-compose.yml -f docker-compose.local-gcp.yml down -v
	docker volume prune -f
	@echo "$(GREEN)✓ GCP emulator cleanup complete$(RESET)"

gcp-status: ## Show status of GCP emulators
	@echo "$(BLUE)GCP Emulator Status:$(RESET)"
	@echo -n "Pub/Sub Emulator: "
	@curl -s http://localhost:8085 > /dev/null && echo "$(GREEN)✓ Running$(RESET)" || echo "$(RED)✗ Not running$(RESET)"
	@echo -n "Secret Manager Mock: "
	@docker-compose exec -T secret-manager-mock redis-cli ping > /dev/null 2>&1 && echo "$(GREEN)✓ Running$(RESET)" || echo "$(RED)✗ Not running$(RESET)"

# Validator Ecosystem Demo
demo: ## Run complete validator ecosystem demo
	@echo "$(BLUE)Starting Validator Ecosystem Demo...$(RESET)"
	python local-validators/run_demo.py

demo-short: ## Run short demo (60 seconds)
	@echo "$(BLUE)Starting Short Demo...$(RESET)"
	python local-validators/run_demo.py --duration 60

demo-long: ## Run long demo (300 seconds)
	@echo "$(BLUE)Starting Long Demo...$(RESET)"
	python local-validators/run_demo.py --duration 300

run-validator1: ## Run validator 1 simulation
	@echo "$(BLUE)Starting Validator Alpha...$(RESET)"
	PUBSUB_EMULATOR_HOST=localhost:8085 python local-validators/validator_simulator.py local-validators/validator1.json

run-validator2: ## Run validator 2 simulation
	@echo "$(BLUE)Starting Validator Beta...$(RESET)"
	PUBSUB_EMULATOR_HOST=localhost:8085 python local-validators/validator_simulator.py local-validators/validator2.json

run-validator1-short: ## Run validator 1 simulation (30 seconds)
	@echo "$(BLUE)Starting Validator Alpha (30 seconds)...$(RESET)"
	PUBSUB_EMULATOR_HOST=localhost:8085 python local-validators/validator_simulator.py local-validators/validator1.json --duration 30

run-validator2-short: ## Run validator 2 simulation (30 seconds)
	@echo "$(BLUE)Starting Validator Beta (30 seconds)...$(RESET)"
	PUBSUB_EMULATOR_HOST=localhost:8085 python local-validators/validator_simulator.py local-validators/validator2.json --duration 30

monitor-pubsub: ## Start Pub/Sub monitor dashboard
	@echo "$(BLUE)Starting Pub/Sub Monitor...$(RESET)"
	PUBSUB_EMULATOR_HOST=localhost:8085 python local-validators/pubsub_monitor.py

setup-validators: ## Setup validator simulation environment
	@echo "$(BLUE)Setting up validator simulation environment...$(RESET)"
	chmod +x local-validators/validator_simulator.py
	chmod +x local-validators/pubsub_monitor.py
	chmod +x local-validators/run_demo.py
	chmod +x scripts/test-pubsub.py
	@echo "$(GREEN)✓ Validator simulation environment ready$(RESET)"

test-pubsub-emulator: ## Test Pub/Sub emulator connectivity
	@echo "$(BLUE)Testing Pub/Sub emulator...$(RESET)"
	PUBSUB_EMULATOR_HOST=localhost:8085 python3 scripts/test-pubsub.py

simple-demo: ## Run simple step-by-step demo
	@echo "$(BLUE)Starting Simple Demo...$(RESET)"
	chmod +x scripts/simple-demo.sh
	./scripts/simple-demo.sh

local-demo: ## Run demo with API locally (avoids Docker issues)
	@echo "$(BLUE)Starting Local Demo...$(RESET)"
	chmod +x scripts/local-demo.sh
	./scripts/local-demo.sh

test-validator-communication: ## Test two validators communicating (30 seconds)
	@echo "$(BLUE)Testing validator communication...$(RESET)"
	@echo "$(YELLOW)This will show real-time bidirectional communication between validators$(RESET)"
	cd $(shell pwd) && python -c "\
import asyncio, sys, os; \
sys.path.append('local-validators'); \
os.environ['PUBSUB_EMULATOR_HOST'] = 'localhost:8085'; \
os.environ['GCP_PROJECT_ID'] = 'local-project'; \
from validator_simulator import ValidatorSimulator; \
async def test(): \
    print('🎭 Starting two validators for communication test...'); \
    v1 = ValidatorSimulator('local-validators/validator1.json', 'http://localhost:8000'); \
    v2 = ValidatorSimulator('local-validators/validator2.json', 'http://localhost:8000'); \
    results = await asyncio.gather(v1.run(30), v2.run(30)); \
    print(f'✅ Both validators completed successfully: {results}'); \
asyncio.run(test())"
