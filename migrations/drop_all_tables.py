"""
Drop all tables from the database.

USE WITH EXTREME CAUTION. This is a destructive operation.
"""

import os
from sqlalchemy import create_engine, inspect
from core.config import settings
from core.models import Base  # Import the Base metadata

def drop_all_tables():
    """Connects to the database and drops all tables."""
    if not settings.DATABASE_URL:
        raise ValueError("DATABASE_URL environment variable not set.")

    # Prompt for confirmation in a production environment
    if settings.ENVIRONMENT == "production":
        confirm = input(
            "⚠️ You are about to drop all tables in the PRODUCTION database. "
            "This is irreversible and will delete all data. "
            "Type 'confirm' to proceed: "
        )
        if confirm != "confirm":
            print("Operation cancelled.")
            return

    engine = create_engine(settings.DATABASE_URL)
    
    print("Connecting to the database to drop tables...")
    
    # Check existing tables
    inspector = inspect(engine)
    table_names = inspector.get_table_names()
    
    if not table_names:
        print("No tables found to drop.")
        return

    print(f"Found tables: {', '.join(table_names)}")
    print("Dropping all tables...")
    
    # Drop all tables defined in the metadata
    Base.metadata.drop_all(engine)
    
    print("✅ All tables dropped successfully.")

if __name__ == "__main__":
    drop_all_tables()
