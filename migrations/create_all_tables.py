"""
Create all database tables based on the models defined in core/models.py.

This script connects to the database specified by the DATABASE_URL environment
variable and creates any tables that do not already exist.
"""

from sqlalchemy import create_engine
from core.config import settings
from core.models import Base  # Import the Base metadata

def create_all_tables():
    """Connects to the database and creates all tables."""
    if not settings.DATABASE_URL:
        raise ValueError("DATABASE_URL environment variable not set.")

    engine = create_engine(settings.DATABASE_URL)
    
    print("Connecting to the database to create tables...")
    
    # Create all tables defined in the metadata
    Base.metadata.create_all(engine)
    
    print("✅ All tables created successfully (if they didn't already exist).")

if __name__ == "__main__":
    create_all_tables()
