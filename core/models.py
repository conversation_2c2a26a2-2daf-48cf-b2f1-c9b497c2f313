"""
Shared database models for Validator Token Gateway.

These models are used by both local development and production environments
to ensure complete consistency.
"""

from datetime import datetime, timezone

from pydantic import BaseModel
from sqlalchemy import Boolean, Column, DateTime, Float, Index, Integer, String
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


# SQLAlchemy Database Models
class NeuronModel(Base):
    """neuron(validator/miner) database model."""

    __tablename__ = "neurons"

    id = Column(Integer, primary_key=True, index=True)
    hotkey = Column(String, index=True)
    is_validator = Column(Boolean, default=True)
    environment = Column(String, index=True)  # production, development
    uid = Column(Integer, index=True)
    stake = Column(Float, default=0.0)
    trust = Column(Float, default=0.0)
    created_at = Column(DateTime, default=datetime.now(tz=timezone.utc))
    last_active = Column(DateTime, default=datetime.now(tz=timezone.utc))
    is_active = Column(Boolean, default=True)

    # Create composite unique constraint for hotkey + environment
    __table_args__ = (
        Index("idx_neuron_hotkey_env", "hotkey", "environment", unique=True),
    )


class TokenModel(Base):
    """JWT Token database model."""

    __tablename__ = "tokens"

    id = Column(Integer, primary_key=True, index=True)
    token = Column(String, index=True)
    hotkey = Column(String, index=True)
    environment = Column(String, index=True)  # production, development
    uid = Column(Integer)
    created_at = Column(DateTime, default=datetime.now(tz=timezone.utc))
    expires_at = Column(DateTime)

    # Create composite index for efficient lookups
    __table_args__ = (
        Index("idx_token_hotkey_env", "hotkey", "environment"),
    )


class GcpTokenModel(Base):
    """Gcp Token database model."""

    __tablename__ = "gcp_tokens"

    id = Column(Integer, primary_key=True, index=True)
    hotkey = Column(String, index=True)
    environment = Column(String, index=True)  # production, development
    uid = Column(Integer)
    created_at = Column(DateTime, default=datetime.now(tz=timezone.utc))
    expires_at = Column(DateTime)
    scopes = Column(String)
    target_service_account = Column(String)

    # Create composite index for efficient lookups
    __table_args__ = (
        Index("idx_gcp_token_hotkey_env", "hotkey", "environment"),
    )


class RateLimitModel(Base):
    """Rate limiting database model."""

    __tablename__ = "rate_limits"

    id = Column(Integer, primary_key=True, index=True)
    client_ip = Column(String, index=True)
    environment = Column(String, index=True)  # production, development
    endpoint = Column(String, index=True)
    request_count = Column(Integer, default=1)
    window_start = Column(DateTime, default=datetime.now(tz=timezone.utc))
    created_at = Column(DateTime, default=datetime.now(tz=timezone.utc))

    # Create composite index for efficient lookups
    __table_args__ = (
        Index("idx_rate_limit_lookup", "client_ip", "environment", "endpoint", "window_start"),
    )


# Pydantic Response Models
class Neuron(BaseModel):
    """Neuron response model."""

    hotkey: str
    uid: int
    stake: float
    trust: float
    is_validator: bool

    class Config:
        from_attributes = True


class Token(BaseModel):
    """Token response model."""

    access_token: str
    token_type: str


class GcpToken(BaseModel):
    """Gcp token response model."""

    access_token: str
    expires_in: int
    token_type: str = "Bearer"
    scope: str

    class Config:
        from_attributes = True


class AuthRequest(BaseModel):
    """Authentication request model."""

    hotkey: str
    signature: str
    message: str


class HealthResponse(BaseModel):
    """Health check response model."""

    status: str
    timestamp: str
    environment: str
    version: str
