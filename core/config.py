"""
Shared configuration for Validator Token Gateway.

This configuration is used by both local development and production environments
to ensure complete consistency.
"""

import os

from pydantic_settings import BaseSettings

try:
    from google.cloud import secretmanager
except ImportError:
    secretmanager = None


def get_secret(secret_id: str) -> str | None:
    """Retrieve secret from GCP Secret Manager if running in cloud environment."""
    # Only attempt to access Secret Manager in non-local environments
    if os.getenv("ENVIRONMENT") not in ["development", "testing", "local-gcp"]:
        try:
            if not secretmanager:
                return None

            project_id = os.getenv("GCP_PROJECT_ID")
            if not project_id:
                return None

            client = secretmanager.SecretManagerServiceClient()
            name = f"projects/{project_id}/secrets/{secret_id}/versions/latest"
            response = client.access_secret_version(name=name)
            return response.payload.data.decode("UTF-8")
        except Exception as e:
            print(f"Error accessing secret {secret_id}: {e}")
            return None
    return None


class Settings(BaseSettings):
    """Shared settings for all environments."""

    # Database settings
    DATABASE_URL: str = os.getenv("DATABASE_URL") or get_secret(
        f"{os.getenv('SERVICE_NAME', 'validator-token-gateway')}-db-url"
    )

    # Redis settings (for local development and GCP emulation only)
    REDIS_HOST: str = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", "6379"))
    REDIS_PASSWORD: str = os.getenv("REDIS_PASSWORD", "")

    # JWT settings
    JWT_SECRET: str = (
        os.getenv("JWT_SECRET")
        or get_secret(f"{os.getenv('SERVICE_NAME', 'validator-token-gateway')}-jwt-secret")
        or "your-secret-key"
    )
    TOKEN_EXPIRY: int = int(os.getenv("TOKEN_EXPIRY", "3600"))  # 1 hour

    # Rate limiting
    RATE_LIMIT: int = int(os.getenv("RATE_LIMIT", "100"))  # requests per minute

    # CORS (FastAPI only)
    ALLOWED_ORIGINS: list = os.getenv("ALLOWED_ORIGINS", "*").split(",")

    # Bittensor settings
    BITTENSOR_NETWORK: str = os.getenv("BITTENSOR_NETWORK", "finney")
    BITTENSOR_NETUID: int = int(os.getenv("BITTENSOR_NETUID", "27"))  # Default to subnet 27
    BITTENSOR_SANDBOX_NETUID: int = int(os.getenv("BITTENSOR_SANDBOX_NETUID", "15"))  # Sandbox subnet
    AUTH_MESSAGE_TEMPLATE: str = os.getenv(
        "AUTH_MESSAGE_TEMPLATE", "Authenticate to Bittensor Subnet {netuid}"
    )
    AUTH_NONCE: str = os.getenv("AUTH_NONCE", "validator-token-gateway-auth")
    REQUIRE_TRUST: bool = os.getenv("REQUIRE_TRUST", "False").lower() == "true"
    MIN_STAKE: float = float(os.getenv("MIN_STAKE", "0"))  # Minimum stake required

    # Environment (development, staging, production)
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")

    # Google Cloud settings
    GCP_PROJECT_ID: str = os.getenv("GCP_PROJECT_ID", "")
    GCP_SERVICE_ACCOUNT_FILE: str = os.getenv("GCP_SERVICE_ACCOUNT_FILE", "")
    GCP_SERVICE_ACCOUNT_JSON: str = os.getenv("GCP_SERVICE_ACCOUNT_JSON", "{}")
    GCP_PUBSUB_SERVICE_ACCOUNT: str = os.getenv("GCP_PUBSUB_SERVICE_ACCOUNT", "")

    # Pub/Sub Topics
    GCP_PUBSUB_ALLOCATION_EVENTS: str = os.getenv("GCP_PUBSUB_ALLOCATION_EVENTS", "allocation-events")
    GCP_PUBSUB_MINER_EVENTS: str = os.getenv("GCP_PUBSUB_MINER_EVENTS", "miner-events")
    GCP_PUBSUB_SYSTEM_EVENTS: str = os.getenv("GCP_PUBSUB_SYSTEM_EVENTS", "system-events")
    GCP_PUBSUB_VALIDATION_EVENTS: str = os.getenv("GCP_PUBSUB_VALIDATION_EVENTS", "validation-events")

    # Pub/Sub token settings
    PUBSUB_TOKEN_LIFETIME: int = int(os.getenv("PUBSUB_TOKEN_LIFETIME", "3600"))  # 1 hour
    # Per validator hourly limit
    PUBSUB_TOKEN_HOURLY_LIMIT: int = int(os.getenv("PUBSUB_TOKEN_HOURLY_LIMIT", "30"))

    # Environment-aware properties
    @property
    def active_netuid(self) -> int:
        """Get the active Bittensor netuid based on environment."""
        return (
            self.BITTENSOR_SANDBOX_NETUID
            if self.ENVIRONMENT == "development"
            else self.BITTENSOR_NETUID
        )

    @property
    def active_network(self) -> str:
        """Get the active Bittensor network based on environment."""
        return "test" if self.ENVIRONMENT == "development" else self.BITTENSOR_NETWORK

    @property
    def auth_message(self) -> str:
        """Get the authentication message with the active netuid."""
        return self.AUTH_MESSAGE_TEMPLATE.format(netuid=self.active_netuid)

    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.ENVIRONMENT == "production"

    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.ENVIRONMENT in ["development", "testing", "local-gcp"]


# Global settings instance
settings = Settings()
