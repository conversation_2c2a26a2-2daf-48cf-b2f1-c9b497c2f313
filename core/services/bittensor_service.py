"""
Shared Bittensor service for Validator Token Gateway.

This service handles all Bittensor network interactions and is used by both
local development and production environments.
"""

import hashlib

import bittensor

from core.config import settings


class BittensorService:
    """Service for interacting with the Bittensor network."""

    @staticmethod
    def get_components():
        """Get Bittensor subtensor and metagraph components with error handling."""
        try:
            # Create subtensor connection using environment-aware network
            subtensor = bittensor.subtensor(network=settings.active_network)
            # Create metagraph for the configured subnet
            metagraph = bittensor.metagraph(
                netuid=settings.active_netuid, subtensor=subtensor
            )
            # Sync the metagraph to get the latest state
            metagraph.sync()
            return subtensor, metagraph
        except Exception as e:
            error_msg = str(e)
            print(f"❌ Bittensor network connection failed: {error_msg}")

            # If this is a WebSocket 503 error and we're in development, provide guidance
            if "503" in error_msg and "WebSocket" in error_msg:
                if settings.ENVIRONMENT == "development":
                    print("🔧 Development tip: Test network appears to be down.")
                    print("   This is a known issue with Bittensor test network infrastructure.")
                    print("   Your application code is correct - this is an external service issue.")
                    print("   Falling back to mock mode for development...")

                    # Return mock components for development
                    return BittensorService._create_mock_components()

            # Re-raise the original exception
            raise e

    @staticmethod
    def _create_mock_components():
        """Create mock Bittensor components for development when network is unavailable."""

        class MockSubtensor:
            def __init__(self):
                self.network = settings.active_network
                self.netuid = settings.active_netuid

            def get_current_block(self):
                return 1000

            def get_balance(self, address):
                return 1000000000000  # 1 TAO

        class MockMetagraph:
            def __init__(self, netuid, subtensor):
                self.netuid = netuid
                self.subtensor = subtensor
                # Mock validator data for development
                self.hotkeys = [
                    "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY",
                    "5FHneW46xGXgs5mUiveU4sbTyGBzmstUspZC92UhjJM694ty",
                    "5FLSigC9HGRKVhB9FiEo4Y3koPsNmBmLJbpXg2mp1hXcS59Y"
                ]
                self.stakes = [1000000000000, 2000000000000, 1500000000000]

            def sync(self):
                print("🔧 Using mock metagraph data for development")
                return True

        mock_subtensor = MockSubtensor()
        mock_metagraph = MockMetagraph(settings.active_netuid, mock_subtensor)
        mock_metagraph.sync()

        print(f"✅ Mock components created: {len(mock_metagraph.hotkeys)} mock validators")
        return mock_subtensor, mock_metagraph

    @staticmethod
    def verify_signature(signature: str, message: str, ss58_address: str) -> bool:
        """Verify a signature against a message and SS58 address."""
        try:
            # Create a keypair from the SS58 address
            keypair = bittensor.Keypair(ss58_address=ss58_address)

            # Convert hex signature back to bytes
            signature_bytes = bytes.fromhex(signature)

            # Try method 1: Verify against message directly (most common)
            message_bytes = message.encode()
            result = keypair.verify(message_bytes, signature_bytes)

            if result:
                return True

            # Try method 2: Verify against hash of message (fallback)
            data_hash = hashlib.sha256(message.encode()).digest()
            result = keypair.verify(data_hash, signature_bytes)

            return result
        except Exception as e:
            # Keep important error logging for troubleshooting
            print(f"Signature verification error: {str(e)}")
            return False

    @staticmethod
    def is_validator(metagraph, ss58_address: str) -> tuple[bool, int, float, float]:
        """
        Check if a hotkey is a validator in the metagraph.

        Returns:
            tuple: (is_validator, uid, stake, trust)
        """
        # Check if the hotkey is in the metagraph
        if ss58_address not in metagraph.hotkeys:
            return False, -1, 0.0, 0.0

        # Get the UID for the hotkey
        uid = metagraph.hotkeys.index(ss58_address)

        # Get stake and trust
        stake = float(metagraph.S[uid])
        trust = float(metagraph.T[uid])

        # Validators have stake (S), miner usually doesn't
        # This is SN27 side of setting for validator permit stake
        is_validator = stake > 1.0e4

        return is_validator, uid, stake, trust
