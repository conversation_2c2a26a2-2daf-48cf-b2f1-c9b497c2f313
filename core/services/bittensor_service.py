"""
Shared Bittensor service for Validator Token Gateway.

This service handles all Bittensor network interactions and is used by both
local development and production environments.
"""

import hashlib
import time
from typing import Tuple

import bittensor

from core.config import settings


class BittensorService:
    """Service for interacting with the Bittensor network."""

    @staticmethod
    def get_components():
        """Get Bittensor subtensor and metagraph components with network fallback."""
        # Define network configurations with fallback
        network_configs = [
            (settings.active_network, settings.active_netuid),
        ]

        # Add fallback to finney if currently using test network
        if settings.active_network == "test":
            network_configs.append(("finney", 27))

        last_error = None
        for network, netuid in network_configs:
            try:
                print(f"Attempting to connect to {network} network (netuid {netuid})...")

                # Create subtensor connection
                subtensor = bittensor.subtensor(network=network)

                # Create and sync metagraph
                metagraph = bittensor.metagraph(netuid=netuid, subtensor=subtensor)
                metagraph.sync()

                print(f"✅ Successfully connected to {network} network")
                return subtensor, metagraph

            except Exception as e:
                error_msg = str(e)
                print(f"❌ Failed to connect to {network} network: {error_msg[:100]}...")
                last_error = e

                # If this is a WebSocket 503 error, continue to fallback
                if "503" in error_msg or "WebSocket" in error_msg:
                    continue
                else:
                    # For other errors, re-raise immediately
                    raise e

        # If all networks failed, raise the last error
        if last_error:
            raise last_error
        else:
            raise RuntimeError("No network configurations available")

    @staticmethod
    def verify_signature(signature: str, message: str, ss58_address: str) -> bool:
        """Verify a signature against a message and SS58 address."""
        try:
            # Create a keypair from the SS58 address
            keypair = bittensor.Keypair(ss58_address=ss58_address)

            # Convert hex signature back to bytes
            signature_bytes = bytes.fromhex(signature)

            # Try method 1: Verify against message directly (most common)
            message_bytes = message.encode()
            result = keypair.verify(message_bytes, signature_bytes)

            if result:
                return True

            # Try method 2: Verify against hash of message (fallback)
            data_hash = hashlib.sha256(message.encode()).digest()
            result = keypair.verify(data_hash, signature_bytes)

            return result
        except Exception as e:
            # Keep important error logging for troubleshooting
            print(f"Signature verification error: {str(e)}")
            return False

    @staticmethod
    def is_validator(metagraph, ss58_address: str) -> tuple[bool, int, float, float]:
        """
        Check if a hotkey is a validator in the metagraph.

        Returns:
            tuple: (is_validator, uid, stake, trust)
        """
        # Check if the hotkey is in the metagraph
        if ss58_address not in metagraph.hotkeys:
            return False, -1, 0.0, 0.0

        # Get the UID for the hotkey
        uid = metagraph.hotkeys.index(ss58_address)

        # Get stake and trust
        stake = float(metagraph.S[uid])
        trust = float(metagraph.T[uid])

        # Validators have stake (S), miner usually doesn't
        # This is SN27 side of setting for validator permit stake
        is_validator = stake > 1.0e4

        return is_validator, uid, stake, trust
