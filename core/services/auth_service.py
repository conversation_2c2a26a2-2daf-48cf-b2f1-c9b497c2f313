"""
Shared authentication service for Validator Token Gateway.

This service handles validator authentication and is used by both
local development and production environments.
"""

from datetime import datetime, timezone

from sqlalchemy.orm import Session

from core.config import settings
from core.models import Neuron, NeuronModel
from core.services.bittensor_service import BittensorService
from core.services.neuron_service import NeuronService


class AuthService:
    """Service for validator/miner authentication."""

    @staticmethod
    def authenticate_validator_with_signature(
        hotkey: str, signature: str, db: Session
    ) -> Neuron | None:
        """
        Authenticate a validator using Bittensor signature.

        Args:
            hotkey: Validator's hotkey (SS58 address)
            signature: Cryptographic signature
            db: Database session

        Returns:
            Validator object if authentication successful, None otherwise
        """
        try:
            # Get bittensor components
            _, metagraph = BittensorService.get_components()

            # Check if the hotkey is a validator
            is_validator, uid, stake, trust = BittensorService.is_validator(
                metagraph, hotkey
            )

            if not is_validator:
                return None

            # Additional check: validators should have trust score
            if trust <= 0 and settings.REQUIRE_TRUST:
                return None

            # Check minimum stake requirement
            if stake < settings.MIN_STAKE:
                return None

            # Create the message that the validator would have signed
            message = settings.auth_message

            # Verify the signature
            signature_valid = BittensorService.verify_signature(
                signature, message, hotkey
            )
            if not signature_valid:
                return None

            # Get or create validator in the database
            validator_db = NeuronService.get_or_create_neuron(
                db, hotkey, uid, stake, trust, is_validator
            )

            # Convert to Pydantic model
            validator = NeuronService.to_pydantic(validator_db)

            return validator

        except Exception as e:
            # Keep important error logging for troubleshooting
            print(f"Authentication failed: {str(e)}")
            return None

    @staticmethod
    def authenticate_validator(request, db: Session) -> Neuron | None:
        """
        Authenticate a validator from HTTP request.

        Args:
            request: HTTP request object
            db: Database session

        Returns:
            Validator object if authentication successful, None otherwise
        """
        try:
            # Get authorization header
            authorization = request.headers.get("Authorization")
            if not authorization:
                return None

            # Parse Bittensor auth header
            auth_data = AuthService.parse_bittensor_auth_header(authorization)
            if not auth_data:
                return None

            hotkey, signature = auth_data

            # Authenticate with signature
            result = AuthService.authenticate_validator_with_signature(
                hotkey, signature, db
            )
            return result

        except Exception as e:
            # Keep important error logging for troubleshooting
            print(f"Request authentication failed: {str(e)}")
            return None

    @staticmethod
    def authenticate_validator_from_token(request, db: Session) -> Neuron | None:
        """
        Authenticate a validator from JWT token in HTTP request.

        Args:
            request: HTTP request object
            db: Database session

        Returns:
            Validator object if authentication successful, None otherwise
        """
        try:
            # Get authorization header
            authorization = request.headers.get("Authorization")
            if not authorization:
                return None

            # Parse Bearer token
            token = AuthService.parse_bearer_token(authorization)
            if not token:
                return None

            # Get validator from token
            return AuthService.get_neuron_from_token(token, db)

        except Exception as e:
            # Keep important error logging for troubleshooting
            print(f"Token authentication failed: {str(e)}")
            return None

    @staticmethod
    def authenticate_miner_with_signature(
        hotkey: str, signature: str, db: Session
    ) -> Neuron | None:
        """
        Authenticate a miner using Bittensor signature.

        Args:
            hotkey: Miner's hotkey (SS58 address)
            signature: Cryptographic signature
            db: Database session

        Returns:
            Miner object if authentication successful, None otherwise
        """
        try:
            # Get bittensor components
            _, metagraph = BittensorService.get_components()

            # Check if the hotkey is a validator
            _, uid, stake, trust = BittensorService.is_validator(
                metagraph, hotkey
            )

            # Create the message that the validator would have signed
            message = settings.auth_message

            # Verify the signature
            signature_valid = BittensorService.verify_signature(
                signature, message, hotkey
            )
            if not signature_valid:
                return None

            # Get or create validator in the database
            neuron_db = NeuronService.get_or_create_neuron(
                db, hotkey, uid, stake, trust, False
            )

            # Convert to Pydantic model
            miner = NeuronService.to_pydantic(neuron_db)

            return miner

        except Exception as e:
            # Keep important error logging for troubleshooting
            print(f"Authentication failed: {str(e)}")
            return None

    @staticmethod
    def authenticate_miner(request, db: Session) -> Neuron | None:
        """
        Authenticate a miner from HTTP request.

        Args:
            request: HTTP request object
            db: Database session

        Returns:
            Miner object if authentication successful, None otherwise
        """
        try:
            # Get authorization header
            authorization = request.headers.get("Authorization")
            if not authorization:
                return None

            # Parse Bittensor auth header
            auth_data = AuthService.parse_bittensor_auth_header(authorization)
            if not auth_data:
                return None

            hotkey, signature = auth_data

            # Authenticate with signature
            result = AuthService.authenticate_miner_with_signature(
                hotkey, signature, db
            )
            return result

        except Exception as e:
            # Keep important error logging for troubleshooting
            print(f"Request authentication failed: {str(e)}")
            return None

    @staticmethod
    def authenticate_miner_from_token(request, db: Session) -> Neuron | None:
        """
        Authenticate a miner from JWT token in HTTP request.

        Args:
            request: HTTP request object
            db: Database session

        Returns:
            Miner object if authentication successful, None otherwise
        """
        try:
            # Get authorization header
            authorization = request.headers.get("Authorization")
            if not authorization:
                return None

            # Parse Bearer token
            token = AuthService.parse_bearer_token(authorization)
            if not token:
                return None

            # Get validator from token
            return AuthService.get_neuron_from_token(token, db)

        except Exception as e:
            # Keep important error logging for troubleshooting
            print(f"Token authentication failed: {str(e)}")
            return None

    @staticmethod
    def parse_bittensor_auth_header(authorization: str) -> tuple[str, str] | None:
        """
        Parse Bittensor authorization header.

        Args:
            authorization: Authorization header value

        Returns:
            Tuple of (hotkey, signature) if valid, None otherwise
        """
        try:
            # Parse authorization header (format: "Bittensor <hotkey>:<signature>")
            parts = authorization.split()
            if len(parts) != 2 or parts[0] != "Bittensor":
                return None

            auth_parts = parts[1].split(":")
            if len(auth_parts) != 2:
                return None

            hotkey, signature = auth_parts
            return hotkey, signature

        except Exception:
            return None

    @staticmethod
    def parse_bearer_token(authorization: str) -> str | None:
        """
        Parse Bearer token from authorization header.

        Args:
            authorization: Authorization header value

        Returns:
            Token string if valid, None otherwise
        """
        try:
            # Parse authorization header (format: "Bearer <token>")
            parts = authorization.split()
            if len(parts) != 2 or parts[0].lower() != "bearer":
                return None

            return parts[1]

        except Exception:
            return None

    @staticmethod
    def get_neuron_from_token(token: str, db: Session) -> Neuron | None:
        """
        Get neuron from JWT token.

        Args:
            token: JWT token string
            db: Database session

        Returns:
            Neuron object if token is valid, None otherwise
        """
        try:
            from core.services.token_service import TokenService

            # Validate token
            is_valid, payload = TokenService.validate_token(token, db)

            if not is_valid:
                return None

            # Get miner from database
            neuron_db = (
                db.query(NeuronModel)
                .filter(NeuronModel.hotkey == payload["sub"])
                .first()
            )

            if not neuron_db:
                return None

            # Update last_active timestamp
            neuron_db.last_active = datetime.now(tz=timezone.utc)
            db.commit()

            # Convert to Pydantic model
            miner = NeuronService.to_pydantic(neuron_db)

            return miner

        except Exception as e:
            # Keep important error logging for troubleshooting
            print(f"Token validation failed: {str(e)}")
            return None
