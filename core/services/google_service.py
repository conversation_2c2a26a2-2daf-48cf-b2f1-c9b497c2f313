"""
Shared Google Cloud service for Validator Token Gateway.

This service handles Google Cloud operations and is used by both
local development and production environments.
"""

import logging
import time
from datetime import datetime, timezone

from google.auth import default
from sqlalchemy.orm import Session

from core.config import settings
from core.models import GcpTokenModel, NeuronModel

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from google.auth import impersonated_credentials
    from google.auth.transport.requests import Request
    from google.oauth2 import service_account
except ImportError:
    impersonated_credentials = None
    Request = None
    service_account = None


class GoogleService:
    """Service for Google Cloud operations."""

    # Pub/Sub scopes required for messaging
    PUBSUB_SCOPE = [
        "https://www.googleapis.com/auth/pubsub",
        "https://www.googleapis.com/auth/cloud-platform",
    ]

    @staticmethod
    def generate_pubsub_token(
        validator_hotkey: str, db: Session, rate_limit_check: bool = True
    ) -> tuple[str, int]:
        """
        Generate a short-lived OAuth2 token for Pub/Sub access.

        Args:
            validator_hotkey: The validator's hotkey
            db: Database session
            rate_limit_check: Whether to check rate limits

        Returns:
            Tuple[str, int]: (token, expiry_time_seconds)
        """
        if not all([impersonated_credentials, Request, service_account]):
            raise RuntimeError("Google Cloud libraries not available")

        # Check if validator exists
        validator = (
            db.query(NeuronModel)
            .filter(NeuronModel.hotkey == validator_hotkey)
            .first()
        )

        if not validator:
            raise ValueError(f"Validator with hotkey {validator_hotkey} not found")

        # Rate limiting check (if enabled) - applied per validator hotkey
        if rate_limit_check:
            from core.services.rate_limit_service import RateLimitService

            if not RateLimitService.check_rate_limit(
                db, validator_hotkey, settings.PUBSUB_TOKEN_HOURLY_LIMIT, 60, "pubsub_token"
            ):
                raise ValueError("Rate limit exceeded for Pub/Sub token generation")

        try:
            # Use Application Default Credentials directly (no impersonation needed)
            target_credentials, _ = default(scopes=GoogleService.PUBSUB_SCOPE)

            # Refresh the credentials to get a token
            target_credentials.refresh(Request())

            # Get token and expiry
            token = target_credentials.token
            expiry = int(time.time() + settings.PUBSUB_TOKEN_LIFETIME)

            # Record token issuance in database for auditing
            token_record = GcpTokenModel(
                hotkey=validator_hotkey,
                uid=validator.uid,
                expires_at=datetime.fromtimestamp(expiry, tz=timezone.utc),
                scopes=",".join(GoogleService.PUBSUB_SCOPE),
                target_service_account=settings.GCP_PUBSUB_SERVICE_ACCOUNT,
            )
            db.add(token_record)
            db.commit()

            logger.info(
                f"Generated Pub/Sub token for validator {validator_hotkey} "
                f"(UID: {validator.uid})"
            )

            return token, expiry

        except Exception as e:
            logger.error(f"Failed to generate Pub/Sub token: {str(e)}")
            raise ValueError(f"Failed to generate Pub/Sub token: {str(e)}") from e

    @staticmethod
    def cleanup_expired_pubsub_tokens(db: Session) -> int:
        """
        Clean up expired Pub/Sub tokens from the database across all environments.

        Args:
            db: Database session

        Returns:
            int: Number of tokens cleaned up
        """
        now = datetime.now(tz=timezone.utc)

        # Get all tokens and filter in Python to handle timezone issues
        all_tokens = db.query(GcpTokenModel).all()
        expired_tokens = []

        for token in all_tokens:
            # Handle both timezone-aware and naive datetimes
            if token.expires_at.tzinfo is None:
                # Database stored naive datetime, compare with naive
                if token.expires_at < now.replace(tzinfo=None):
                    expired_tokens.append(token)
            else:
                # Database stored timezone-aware datetime
                if token.expires_at < now:
                    expired_tokens.append(token)

        count = len(expired_tokens)
        for token in expired_tokens:
            db.delete(token)

        db.commit()
        return count

    @staticmethod
    def get_pubsub_token_stats(db: Session, validator_hotkey: str) -> dict:
        """
        Get Pub/Sub token statistics for a validator.

        Args:
            db: Database session
            validator_hotkey: Validator's hotkey

        Returns:
            dict: Token statistics
        """
        now = datetime.now(tz=timezone.utc)

        # Get all tokens for this validator
        all_tokens = (
            db.query(GcpTokenModel)
            .filter(GcpTokenModel.hotkey == validator_hotkey)
            .all()
        )

        # Count active tokens (handle timezone issues)
        active_count = 0
        for token in all_tokens:
            # Handle both timezone-aware and naive datetimes
            if token.expires_at.tzinfo is None:
                # Database stored naive datetime, compare with naive
                if token.expires_at > now.replace(tzinfo=None):
                    active_count += 1
            else:
                # Database stored timezone-aware datetime
                if token.expires_at > now:
                    active_count += 1

        return {
            "active_tokens": active_count,
            "total_tokens_issued": len(all_tokens),
            "last_token_issued": None,  # Could add this if needed
        }

    @staticmethod
    def get_pubsub_topic_paths() -> dict[str, str]:
        """
        Get all Pub/Sub topic paths.

        Returns:
            dict: Dictionary mapping topic types to their full paths
        """
        project_id = settings.GCP_PROJECT_ID
        return {
            "allocation_events": f"projects/{project_id}/topics/{settings.GCP_PUBSUB_ALLOCATION_EVENTS}",
            "miner_events": f"projects/{project_id}/topics/{settings.GCP_PUBSUB_MINER_EVENTS}",
            "system_events": f"projects/{project_id}/topics/{settings.GCP_PUBSUB_SYSTEM_EVENTS}",
            "validation_events": f"projects/{project_id}/topics/{settings.GCP_PUBSUB_VALIDATION_EVENTS}",
        }

    @staticmethod
    def verify_pubsub_setup() -> dict[str, bool]:
        """
        Verify Pub/Sub setup and configuration.

        Returns:
            dict: Dictionary of verification results
        """
        results = {}

        # Check if project ID is configured
        results["project_id_configured"] = bool(settings.GCP_PROJECT_ID)

        # Check if topic names are configured
        results["topics_configured"] = all([
            settings.GCP_PUBSUB_ALLOCATION_EVENTS,
            settings.GCP_PUBSUB_MINER_EVENTS,
            settings.GCP_PUBSUB_SYSTEM_EVENTS,
            settings.GCP_PUBSUB_VALIDATION_EVENTS,
        ])

        # Check if Google Cloud libraries are available
        results["libraries_available"] = all([impersonated_credentials, Request, service_account])

        # Check if service account is configured (optional for development)
        results["service_account_configured"] = bool(settings.GCP_PUBSUB_SERVICE_ACCOUNT)

        return results
