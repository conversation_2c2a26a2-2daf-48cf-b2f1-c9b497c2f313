"""
Shared neuron service for SN27 Token Gateway.

This service handles neuron database operations and is used by both
local development and production environments.
"""

from datetime import datetime, timezone

from sqlalchemy.orm import Session

from core.config import settings
from core.models import Neuron, NeuronModel


class NeuronService:
    """Service for managing neurons."""

    @staticmethod
    def get_or_create_neuron(
        db: Session, hotkey: str, uid: int, stake: float, trust: float, is_validator: bool,
        environment: str = None,
    ) -> NeuronModel:
        """Get or create a neuron in the database for a specific environment."""
        if environment is None:
            environment = settings.ENVIRONMENT

        # Get or create neuron in the database for this environment
        neuron_db = (
            db.query(NeuronModel)
            .filter(
                NeuronModel.hotkey == hotkey,
                NeuronModel.environment == environment
            )
            .first()
        )

        if not neuron_db:
            # Create new neuron record
            neuron_db = NeuronModel(
                hotkey=hotkey,
                environment=environment,
                uid=uid,
                stake=stake,
                trust=trust,
                is_validator=is_validator,
            )
            db.add(neuron_db)
            db.commit()
            db.refresh(neuron_db)
        else:
            # Update neuron info
            neuron_db.uid = uid
            neuron_db.stake = stake
            neuron_db.trust = trust
            neuron_db.last_active = datetime.now(tz=timezone.utc)
            neuron_db.is_validator = is_validator
            db.commit()

        return neuron_db

    @staticmethod
    def to_pydantic(validator_db: NeuronModel) -> Neuron:
        """Convert SQLAlchemy model to Pydantic model."""
        return Neuron(
            hotkey=validator_db.hotkey,
            uid=validator_db.uid,
            stake=validator_db.stake,
            trust=validator_db.trust,
            is_validator=validator_db.is_validator
        )
