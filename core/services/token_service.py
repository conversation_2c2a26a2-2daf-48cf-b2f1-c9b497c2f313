"""
Shared token service for Validator Token Gateway.

This service handles JWT token operations and is used by both
local development and production environments.
"""

import time
from datetime import datetime, timedelta, timezone

import jwt
from sqlalchemy.orm import Session

from core.config import settings
from core.models import Neuron, Token, TokenModel


class TokenService:
    """Service for managing authentication tokens."""

    @staticmethod
    def create_token(validator: Neuron, db: Session, environment: str = None) -> Token:
        """
        Create a JWT token for a validator and store it in the database.
        If a token already exists for this validator, invalidate it and create a new one.
        """
        if environment is None:
            environment = settings.ENVIRONMENT

        # Check if validator already has an active token in this environment
        existing_token = (
            db.query(TokenModel)
            .filter(
                TokenModel.hotkey == validator.hotkey,
                TokenModel.environment == environment,
                TokenModel.expires_at > datetime.now(tz=timezone.utc),
            )
            .first()
        )

        # If token exists, delete it
        if existing_token:
            db.delete(existing_token)
            db.commit()

        # Generate new token
        expiry_time = time.time() + settings.TOKEN_EXPIRY
        payload = {
            "sub": validator.hotkey,
            "uid": validator.uid,
            "stake": validator.stake,
            "trust": validator.trust,
            "environment": environment,
            "exp": expiry_time,
            "iat": time.time(),
        }

        token = jwt.encode(payload, settings.JWT_SECRET, algorithm="HS256")

        # Store token in database
        token_db = TokenModel(
            token=token,
            hotkey=validator.hotkey,
            environment=environment,
            uid=validator.uid,
            expires_at=datetime.now(tz=timezone.utc)
            + timedelta(seconds=settings.TOKEN_EXPIRY),
        )
        db.add(token_db)
        db.commit()

        return Token(access_token=token, token_type="bearer")

    @staticmethod
    def validate_token(token: str, db: Session) -> tuple[bool, dict]:
        """
        Validate a JWT token.

        Returns:
            tuple: (is_valid, payload)
        """
        # Check if token exists in database
        token_db = db.query(TokenModel).filter(TokenModel.token == token).first()
        if not token_db:
            return False, {}

        # Check if token has expired
        now = datetime.now(tz=timezone.utc)
        # Handle both timezone-aware and naive datetimes
        if token_db.expires_at.tzinfo is None:
            # Database stored naive datetime, compare with naive
            if token_db.expires_at < now.replace(tzinfo=None):
                return False, {}
        else:
            # Database stored timezone-aware datetime
            if token_db.expires_at < now:
                return False, {}

        # Decode and validate the token
        try:
            payload = jwt.decode(token, settings.JWT_SECRET, algorithms=["HS256"])
            return True, payload
        except jwt.PyJWTError:
            return False, {}

    @staticmethod
    def cleanup_expired_tokens(db: Session) -> int:
        """
        Clean up expired tokens from the database across all environments.

        Returns:
            int: Number of tokens cleaned up
        """
        expired_tokens = (
            db.query(TokenModel)
            .filter(TokenModel.expires_at < datetime.now(tz=timezone.utc))
            .all()
        )

        count = len(expired_tokens)
        for token in expired_tokens:
            db.delete(token)

        db.commit()
        return count
