"""
Shared rate limiting service for Validator Token Gateway.

This service handles rate limiting using database storage and is used by both
local development and production environments.
"""

from datetime import datetime, timedelta, timezone

from sqlalchemy.orm import Session

from core.config import settings
from core.models import RateLimitModel


class RateLimitService:
    """Service for managing rate limiting."""

    @staticmethod
    def check_rate_limit(
        db: Session, client_ip: str, limit: int, window_minutes: int = 1, endpoint: str = "default", environment: str = None
    ) -> bool:
        """
        Check if a client has exceeded the rate limit for a specific environment.

        Args:
            db: Database session
            client_ip: Client IP address
            limit: Maximum requests allowed in the window
            window_minutes: Time window in minutes
            endpoint: Endpoint identifier for separate rate limiting
            environment: Environment (production, development)

        Returns:
            bool: True if request is allowed, False if rate limit exceeded
        """
        if environment is None:
            environment = settings.ENVIRONMENT

        now = datetime.now(tz=timezone.utc)
        window_start = now - timedelta(minutes=window_minutes)

        # Get rate limit record for this environment (without window_start filter to avoid timezone issues)
        rate_limit = (
            db.query(RateLimitModel)
            .filter(
                RateLimitModel.client_ip == client_ip,
                RateLimitModel.environment == environment,
                RateLimitModel.endpoint == endpoint,
            )
            .order_by(RateLimitModel.window_start.desc())
            .first()
        )

        if not rate_limit:
            # Create new rate limit record
            rate_limit = RateLimitModel(
                client_ip=client_ip,
                environment=environment,
                endpoint=endpoint,
                request_count=1,
                window_start=now,
            )
            db.add(rate_limit)
            db.commit()
            return True

        # Check if we're still in the same window (handle timezone issues)
        rate_limit_window_start = rate_limit.window_start
        if rate_limit_window_start.tzinfo is None:
            # Database stored naive datetime, compare with naive
            window_start_naive = window_start.replace(tzinfo=None)
            is_same_window = rate_limit_window_start >= window_start_naive
        else:
            # Database stored timezone-aware datetime
            is_same_window = rate_limit_window_start >= window_start

        if is_same_window:
            # Same window, check if limit exceeded
            if rate_limit.request_count >= limit:
                return False

            # Increment counter
            rate_limit.request_count += 1
            db.commit()
            return True
        else:
            # New window, reset counter
            rate_limit.request_count = 1
            rate_limit.window_start = now
            db.commit()
            return True

    @staticmethod
    def cleanup_old_rate_limits(db: Session, hours_old: int = 24) -> int:
        """
        Clean up old rate limit records across all environments.

        Args:
            db: Database session
            hours_old: Remove records older than this many hours

        Returns:
            int: Number of records cleaned up
        """
        cutoff_time = datetime.now(tz=timezone.utc) - timedelta(hours=hours_old)

        # Get all records and filter in Python to handle timezone issues
        all_records = db.query(RateLimitModel).all()
        old_records = []

        for record in all_records:
            # Handle both timezone-aware and naive datetimes
            if record.created_at.tzinfo is None:
                # Database stored naive datetime, compare with naive
                if record.created_at < cutoff_time.replace(tzinfo=None):
                    old_records.append(record)
            else:
                # Database stored timezone-aware datetime
                if record.created_at < cutoff_time:
                    old_records.append(record)

        count = len(old_records)
        for record in old_records:
            db.delete(record)

        db.commit()
        return count

    @staticmethod
    def get_rate_limit_status(
        db: Session, client_ip: str, endpoint: str = "default", window_minutes: int = 1, environment: str = None
    ) -> dict:
        """
        Get current rate limit status for a client in a specific environment.

        Args:
            db: Database session
            client_ip: Client IP address
            endpoint: Endpoint identifier
            window_minutes: Time window in minutes
            environment: Environment (production, development)

        Returns:
            dict: Rate limit status information
        """
        if environment is None:
            environment = settings.ENVIRONMENT

        now = datetime.now(tz=timezone.utc)
        window_start = now - timedelta(minutes=window_minutes)

        rate_limit = (
            db.query(RateLimitModel)
            .filter(
                RateLimitModel.client_ip == client_ip,
                RateLimitModel.environment == environment,
                RateLimitModel.endpoint == endpoint,
            )
            .order_by(RateLimitModel.window_start.desc())
            .first()
        )

        if not rate_limit:
            return {
                "requests_made": 0,
                "window_start": now.isoformat(),
                "window_end": (now + timedelta(minutes=window_minutes)).isoformat(),
                "environment": environment,
            }

        return {
            "requests_made": rate_limit.request_count,
            "window_start": rate_limit.window_start.isoformat(),
            "window_end": (rate_limit.window_start + timedelta(minutes=window_minutes)).isoformat(),
            "environment": environment,
        }
