# Validator Token Gateway - Cloud Function

This Cloud Function replaces the FastAPI microservice architecture with a simpler, serverless approach. It merges the functionality of `/auth/token` and `/auth/pubsub-token` endpoints into a single function.

## 🏗️ Architecture Changes

### Before (FastAPI + Cloud Run)
- FastAPI application with multiple routes
- Redis for caching and rate limiting
- Cloud Run for hosting
- Complex microservice architecture

### After (Cloud Function)
- Single Cloud Function with merged endpoints
- Cloud SQL for all data storage (including rate limiting)
- Simplified serverless architecture
- No Redis dependency

## 📋 Endpoints

### JWT Token Generation
- **POST** `/auth/token` or `/token`
- Requires Bittensor signature authentication
- Returns JWT token for API access

### Pub/Sub Token Generation
- **POST** `/auth/pubsub-token` or `/pubsub-token`
- Requires valid JWT token
- Returns OAuth2 token for Google Pub/Sub access

## 🔧 Configuration

Environment variables:
- `DATABASE_URL` - PostgreSQL connection string
- `JWT_SECRET` - Secret for JWT token signing
- `GCP_PROJECT_ID` - Google Cloud project ID
- `GCP_PUBSUB_SERVICE_ACCOUNT` - Service account for Pub/Sub impersonation
- `GCP_PUBSUB_ALLOCATION_EVENTS` - Allocation events topic name
- `GCP_PUBSUB_MINER_EVENTS` - Miner events topic name
- `GCP_PUBSUB_SYSTEM_EVENTS` - System events topic name
- `GCP_PUBSUB_VALIDATION_EVENTS` - Validation events topic name
- `BITTENSOR_NETWORK` - Bittensor network (finney/test)
- `BITTENSOR_NETUID` - Subnet ID
- `ENVIRONMENT` - Environment (development/production)

## 🚀 Deployment

### Prerequisites
1. Google Cloud SDK installed and configured
2. Terraform installed
3. Required environment variables set

### Deploy
```bash
# Set environment variables
export GCP_PROJECT_ID="your-project-id"
export GCP_REGION="us-central1"

# Run deployment script
./scripts/deploy-function.sh
```

### Manual Deployment
```bash
# Package function (IMPORTANT: Must include core module)
mkdir -p /tmp/function-package
cp -r functions/auth_function/* /tmp/function-package/
cp -r core /tmp/function-package/  # CRITICAL: Include core module
cd /tmp/function-package
zip -r function-source.zip . -x "*.pyc" "*__pycache__*"

# Upload to Cloud Storage
gsutil cp function-source.zip gs://your-bucket/

# Deploy with Terraform
cd ../../terraform
terraform apply
```

### Test Packaging
```bash
# Verify core module is included in package
make test-packaging
```

## 🧪 Testing

### Local Testing
```bash
cd functions/auth_function
python test_function.py
```

### Integration Testing
```bash
# Test deployed function (should return 405 Method Not Allowed)
curl -X GET "https://your-function-url/auth/token"

# Test with proper POST request (requires authentication)
curl -X POST "https://your-function-url/auth/token" \
  -H "Authorization: Bittensor <hotkey>:<signature>"
```

## 📊 Rate Limiting

Rate limiting is now handled by Cloud SQL instead of Redis:
- Stores client IP, request count, and time windows in `rate_limits` table
- Automatic cleanup of old records
- Configurable limits per minute

## 🔒 Authentication

### Bittensor Signature Auth (for JWT tokens)
```
Authorization: Bittensor <hotkey>:<signature>
```

### JWT Token Auth (for Pub/Sub tokens)
```
Authorization: Bearer <jwt_token>
```

## 📈 Monitoring

### Logs
```bash
# View function logs
gcloud functions logs read validator-token-gateway-auth --limit=50

# Follow logs in real-time
gcloud functions logs tail validator-token-gateway-auth
```

### Metrics
- Function invocations
- Error rates
- Execution time
- Memory usage

Available in Google Cloud Console under Cloud Functions.

## 🔄 Migration from FastAPI

### Database Changes
1. Added `rate_limits` table for SQL-based rate limiting
2. Existing tables remain unchanged

### API Compatibility
- All existing endpoints work the same way
- Same request/response formats
- Same authentication methods

### Client Updates
- Update base URL to Cloud Function URL
- No other changes required

## 🛠️ Development

### Local Development
```bash
# Install dependencies
pip install -r requirements.txt

# Set environment variables
export DATABASE_URL="postgresql://user:pass@localhost/db"
export JWT_SECRET="your-secret"

# Run tests
python test_function.py
```

### Adding New Endpoints
1. Add handler function in `main.py`
2. Update routing logic in `auth_handler`
3. Add tests in `test_function.py`
4. Update this README

## 🔍 Troubleshooting

### Common Issues

**Function timeout**
- Increase timeout in Terraform configuration
- Optimize database queries

**Database connection errors**
- Check DATABASE_URL format
- Verify Cloud SQL instance is running
- Check service account permissions

**Authentication failures**
- Verify Bittensor network configuration
- Check JWT secret configuration
- Validate signature format

### Debug Mode
Set `ENVIRONMENT=development` for additional logging.

## 📚 Dependencies

- `functions-framework` - Cloud Functions runtime (includes HTTP handling)
- `sqlalchemy` - Database ORM
- `psycopg2-binary` - PostgreSQL driver
- `PyJWT` - JWT token handling
- `bittensor` - Bittensor network integration
- `google-cloud-*` - Google Cloud services

**Note**: No Flask dependency needed - Cloud Functions framework provides native HTTP handling.
