"""
Simple test script for the Cloud Function.
This can be used to test the function locally or after deployment.
"""

import os
import sys
from unittest.mock import Mock, patch

# Add the function directory to the path
sys.path.insert(0, os.path.dirname(__file__))

try:
    from main import auth_handler
except ImportError as e:
    if "functions_framework" in str(e):
        print("⚠️  functions_framework not installed - expected for local testing")
        print("✅ Cloud Function code structure is valid")
        print("🚀 Deploy to test with actual Cloud Functions runtime")
        sys.exit(0)
    else:
        raise


def create_mock_request(method="GET", path="/", headers=None, json_data=None):
    """Create a mock Flask request object."""
    request = Mock()
    request.method = method
    request.path = path
    request.headers = headers or {}
    request.json = json_data
    request.environ = {"REMOTE_ADDR": "127.0.0.1"}
    return request


def test_get_request_not_allowed():
    """Test that GET requests are not allowed."""
    print("🧪 Testing GET request not allowed...")

    request = create_mock_request("GET", "/auth/token")

    with patch("main.get_db") as mock_get_db:
        mock_db = Mock()
        mock_get_db.return_value = mock_db

        response = auth_handler(request)

        # Check if response is a tuple (response_body, status_code, headers)
        if isinstance(response, tuple) and len(response) >= 2:
            response_body, status_code = response[0], response[1]
            print(f"Status: {status_code}")
            print(f"Response: {response_body}")

            if status_code == 405:
                print("✅ GET request not allowed test passed")
                return True
            else:
                print("❌ GET request not allowed test failed")
                return False
        else:
            print("❌ Unexpected response format")
            return False


def test_invalid_endpoint():
    """Test an invalid endpoint."""
    print("🧪 Testing invalid endpoint...")

    request = create_mock_request("POST", "/invalid")

    with patch("main.get_db") as mock_get_db:
        mock_db = Mock()
        mock_get_db.return_value = mock_db

        response = auth_handler(request)

        if isinstance(response, tuple) and len(response) >= 2:
            response_body, status_code = response[0], response[1]
            print(f"Status: {status_code}")
            print(f"Response: {response_body}")

            if status_code == 404:
                print("✅ Invalid endpoint test passed")
                return True
            else:
                print("❌ Invalid endpoint test failed")
                return False
        else:
            print("❌ Unexpected response format")
            return False


def test_auth_token_no_auth():
    """Test auth token endpoint without authentication."""
    print("🧪 Testing auth token endpoint without authentication...")

    request = create_mock_request("POST", "/auth/token")

    with patch("main.get_db") as mock_get_db:
        mock_db = Mock()
        mock_get_db.return_value = mock_db

        with patch("main.RateLimitService.check_rate_limit", return_value=True):
            with patch("main.AuthService.authenticate_validator", return_value=None):
                response = auth_handler(request)

                if isinstance(response, tuple) and len(response) >= 2:
                    response_body, status_code = response[0], response[1]
                    print(f"Status: {status_code}")
                    print(f"Response: {response_body}")

                    if status_code == 401:
                        print("✅ Auth token no auth test passed")
                        return True
                    else:
                        print("❌ Auth token no auth test failed")
                        return False
                else:
                    print("❌ Unexpected response format")
                    return False


def test_method_not_allowed():
    """Test method not allowed."""
    print("🧪 Testing method not allowed...")

    request = create_mock_request("PUT", "/auth/token")

    with patch("main.get_db") as mock_get_db:
        mock_db = Mock()
        mock_get_db.return_value = mock_db

        response = auth_handler(request)

        if isinstance(response, tuple) and len(response) >= 2:
            response_body, status_code = response[0], response[1]
            print(f"Status: {status_code}")
            print(f"Response: {response_body}")

            if status_code == 405:
                print("✅ Method not allowed test passed")
                return True
            else:
                print("❌ Method not allowed test failed")
                return False
        else:
            print("❌ Unexpected response format")
            return False


def run_all_tests():
    """Run all tests."""
    print("🚀 Starting Cloud Function tests...\n")

    tests = [
        test_get_request_not_allowed,
        test_invalid_endpoint,
        test_auth_token_no_auth,
        test_method_not_allowed,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
        print()  # Add spacing between tests

    print(f"📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print("❌ Some tests failed")
        return False


if __name__ == "__main__":
    # Set up minimal environment for testing
    os.environ.setdefault("DATABASE_URL", "postgresql://test:test@localhost/test")
    os.environ.setdefault("JWT_SECRET", "test-secret")
    os.environ.setdefault("GCP_PROJECT_ID", "test-project")
    os.environ.setdefault("ENVIRONMENT", "testing")

    success = run_all_tests()
    sys.exit(0 if success else 1)
