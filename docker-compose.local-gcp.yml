# Extended Docker Compose for Local GCP Testing
# This file extends the base docker-compose.yml with GCP emulators

version: '3.8'

services:
  # Extend the API service with GCP emulator configuration
  api:
    environment:
      # Override GCP settings for local testing
      - PUBSUB_EMULATOR_HOST=pubsub-emulator:8085
      - PUBSUB_PROJECT_ID=local-project
      - GCP_PROJECT_ID=local-project
      - GOOGLE_APPLICATION_CREDENTIALS=/app/local-service-account.json

      - ENVIRONMENT=local-gcp
    depends_on:
      - pubsub-emulator
      - secret-manager-mock
      - pubsub-setup

  # Google Cloud Pub/Sub Emulator
  pubsub-emulator:
    image: gcr.io/google.com/cloudsdktool/cloud-sdk:latest
    ports:
      - "8085:8085"
    command: >
      bash -c "
        echo 'Starting Pub/Sub emulator...' &&
        gcloud beta emulators pubsub start --host-port=0.0.0.0:8085 --project=local-project
      "
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8085"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Setup Pub/Sub topics and subscriptions
  pubsub-setup:
    image: gcr.io/google.com/cloudsdktool/cloud-sdk:latest
    depends_on:
      pubsub-emulator:
        condition: service_healthy
    environment:
      - PUBSUB_EMULATOR_HOST=pubsub-emulator:8085
    volumes:
      - ./scripts:/scripts
    command: >
      bash -c "
        echo 'Setting up Pub/Sub topics...' &&
        export PUBSUB_EMULATOR_HOST=pubsub-emulator:8085 &&
        gcloud config set project local-project &&
        gcloud config set disable_usage_reporting true &&

        # Create the 4 main topics (ignore errors if they exist)
        gcloud pubsub topics create allocation-events 2>/dev/null || echo 'Topic allocation-events exists' &&
        gcloud pubsub topics create miner-events 2>/dev/null || echo 'Topic miner-events exists' &&
        gcloud pubsub topics create system-events 2>/dev/null || echo 'Topic system-events exists' &&
        gcloud pubsub topics create validation-events 2>/dev/null || echo 'Topic validation-events exists' &&

        # Create local validator simulation topics (for testing)
        gcloud pubsub topics create validator-announcements 2>/dev/null || echo 'Topic validator-announcements exists' &&
        gcloud pubsub topics create network-updates 2>/dev/null || echo 'Topic network-updates exists' &&
        gcloud pubsub topics create consensus-messages 2>/dev/null || echo 'Topic consensus-messages exists' &&
        gcloud pubsub topics create miner-updates 2>/dev/null || echo 'Topic miner-updates exists' &&
        gcloud pubsub topics create performance-metrics 2>/dev/null || echo 'Topic performance-metrics exists' &&

        echo '🎉 Pub/Sub setup complete!'
      "
    restart: "no"

  # Mock Secret Manager using Redis
  secret-manager-mock:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    command: redis-server --port 6379
    restart: unless-stopped
    volumes:
      - secret_data:/data

  # Secret Manager Setup
  secret-setup:
    image: redis:7-alpine
    depends_on:
      - secret-manager-mock
    command: >
      bash -c "
        echo 'Setting up mock secrets...' &&
        redis-cli -h secret-manager-mock -p 6379 set 'projects/local-project/secrets/jwt-secret/versions/latest' 'local-jwt-secret-for-testing-12345' &&
        redis-cli -h secret-manager-mock -p 6379 set 'projects/local-project/secrets/db-password/versions/latest' 'postgres' &&
        redis-cli -h secret-manager-mock -p 6379 set 'projects/local-project/secrets/redis-password/versions/latest' 'redispassword' &&
        echo 'Mock secrets configured!'
      "
    restart: "no"

  # Cloud SQL Proxy Mock (for testing connection patterns)
  cloudsql-proxy-mock:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=gateway_user
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=validator_gateway
    ports:
      - "5433:5432"  # Different port to simulate Cloud SQL
    volumes:
      - cloudsql_data:/var/lib/postgresql/data
    restart: unless-stopped

  # Redis Mock for Memorystore
  memorystore-mock:
    image: redis:7-alpine
    ports:
      - "6381:6379"
    command: redis-server --requirepass memorystore-password
    volumes:
      - memorystore_data:/data
    restart: unless-stopped

volumes:
  secret_data:
  cloudsql_data:
  memorystore_data:
