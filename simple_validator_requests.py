#!/usr/bin/env python3
"""
Simple Validator HTTP Requests
==============================

Simplified examples showing the exact HTTP requests validators need to make.
Includes both Python requests and curl examples.
"""

import json

import requests

# =============================================================================
# CONFIGURATION
# =============================================================================

API_BASE_URL = "http://localhost:8000"

# Example validator credentials (you need real ones)
VALIDATOR_HOTKEY = "5F3sa2TJAzKLV8HMb6bZpCGmJybyqUQD4S1zUUNM8CTfuXGi"  # Example hotkey
VALIDATOR_SIGNATURE = "0x1234567890abcdef..."  # You need to generate this

# Example JWT token (you get this from authentication)
JWT_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."  # You get this from /auth/token


# =============================================================================
# STEP 1: AUTHENTICATION (Bittensor Signature → JWT Token)
# =============================================================================


def authenticate_validator():
    """
    Authenticate with Bittensor signature to get JWT token.

    Required:
    - Hotkey (SS58 address)
    - Signature of the auth message
    """
    url = f"{API_BASE_URL}/auth/token"

    # Authorization header format: "Bittensor <hotkey>:<signature>"
    headers = {
        "Authorization": f"Bittensor {VALIDATOR_HOTKEY}:{VALIDATOR_SIGNATURE}",
        "Content-Type": "application/json",
    }

    print("🔐 Step 1: Authenticating validator...")
    print(f"URL: {url}")
    print(f"Headers: {headers}")

    response = requests.post(url, headers=headers)

    if response.status_code == 200:
        data = response.json()
        print("✅ Authentication successful!")
        print(f"Response: {json.dumps(data, indent=2)}")
        return data["access_token"]
    else:
        print(f"❌ Authentication failed: {response.status_code}")
        print(f"Response: {response.text}")
        return None


# =============================================================================
# STEP 2: REQUEST PUB/SUB TOKEN (JWT Token → Pub/Sub Token)
# =============================================================================


def request_pubsub_token(jwt_token):
    """
    Request Pub/Sub token using JWT authentication.

    Required:
    - Valid JWT token from Step 1
    """
    url = f"{API_BASE_URL}/auth/pubsub-token"

    # Authorization header format: "Bearer <jwt_token>"
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json",
    }

    print("\n📨 Step 2: Requesting Pub/Sub token...")
    print(f"URL: {url}")
    print(f"Headers: {headers}")

    response = requests.post(url, headers=headers)

    if response.status_code == 200:
        data = response.json()
        print("✅ Pub/Sub token received!")
        print(f"Response: {json.dumps(data, indent=2)}")
        return data
    else:
        print(f"❌ Pub/Sub token request failed: {response.status_code}")
        print(f"Response: {response.text}")
        return None


# =============================================================================
# STEP 3: GET VALIDATOR INFO (Optional)
# =============================================================================


def get_validator_info(jwt_token):
    """
    Get validator information using JWT token.

    Required:
    - Valid JWT token from Step 1
    """
    url = f"{API_BASE_URL}/validators/me/token"

    # Authorization header format: "Bearer <jwt_token>"
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json",
    }

    print("\n👤 Step 3: Getting validator info...")
    print(f"URL: {url}")
    print(f"Headers: {headers}")

    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        data = response.json()
        print("✅ Validator info received!")
        print(f"Response: {json.dumps(data, indent=2)}")
        return data
    else:
        print(f"❌ Validator info request failed: {response.status_code}")
        print(f"Response: {response.text}")
        return None


# =============================================================================
# CURL EXAMPLES
# =============================================================================


def print_curl_examples():
    """Print equivalent curl commands."""
    print("\n" + "=" * 60)
    print("🌐 EQUIVALENT CURL COMMANDS")
    print("=" * 60)

    print("\n1️⃣ Authentication (Bittensor signature → JWT token):")
    print(f"""
curl -X POST "{API_BASE_URL}/auth/token" \\
  -H "Authorization: Bittensor {VALIDATOR_HOTKEY}:{VALIDATOR_SIGNATURE}" \\
  -H "Content-Type: application/json"
""")

    print("\n2️⃣ Request Pub/Sub token (JWT → Pub/Sub token):")
    print(f"""
curl -X POST "{API_BASE_URL}/auth/pubsub-token" \\
  -H "Authorization: Bearer {JWT_TOKEN}" \\
  -H "Content-Type: application/json"
""")

    print("\n3️⃣ Get validator info (optional):")
    print(f"""
curl -X GET "{API_BASE_URL}/validators/me/token" \\
  -H "Authorization: Bearer {JWT_TOKEN}" \\
  -H "Content-Type: application/json"
""")


# =============================================================================
# HOW TO GENERATE SIGNATURE
# =============================================================================


def print_signature_help():
    """Explain how to generate the required signature."""
    print("\n" + "=" * 60)
    print("🔐 HOW TO GENERATE BITTENSOR SIGNATURE")
    print("=" * 60)

    print("""
To generate the signature, you need to:

1. Get the auth message from the API configuration:
   Message format: "Authenticate to Bittensor Subnet {netuid}: validator-token-gateway-auth"
   
   For development (netuid 15):
   "Authenticate to Bittensor Subnet 15: validator-token-gateway-auth"
   
   For production (netuid 27):
   "Authenticate to Bittensor Subnet 27: validator-token-gateway-auth"

2. Sign this message with your validator's hotkey:

   Using Bittensor Python:
   ```python
   import bittensor as bt
   
   # Load your wallet
   wallet = bt.wallet(name="your_wallet", hotkey="your_hotkey")
   
   # The message to sign
   message = "Authenticate to Bittensor Subnet 15: validator-token-gateway-auth"
   
   # Sign the message
   signature = wallet.hotkey.sign(message.encode()).hex()
   print(f"Signature: {signature}")
   ```

3. Use the signature in the Authorization header:
   "Bittensor <your_hotkey_ss58>:<signature>"
""")


# =============================================================================
# MAIN EXAMPLE
# =============================================================================


def main():
    """Run the complete example flow."""
    print("🎭 Simple Validator HTTP Requests Example")
    print("=" * 50)

    print("⚠️  NOTE: This example uses placeholder credentials.")
    print("   You need to replace them with real validator credentials.")
    print()

    # Show the complete flow (will fail with placeholder credentials)
    jwt_token = authenticate_validator()

    if jwt_token:
        pubsub_data = request_pubsub_token(jwt_token)
        validator_info = get_validator_info(jwt_token)

    # Show curl examples
    print_curl_examples()

    # Show how to generate signature
    print_signature_help()


if __name__ == "__main__":
    main()
