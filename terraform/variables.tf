variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

variable "region" {
  description = "GCP Region"
  default     = "us-central1"
  type        = string
}

variable "service_name" {
  description = "Service name for Cloud Function and related resources"
  default     = "validator-token-gateway"
  type        = string
}

variable "bittensor_network" {
  description = "Bittensor network to connect to (e.g., finney, test)"
  type        = string
  default     = "finney"
}

variable "bittensor_netuid" {
  description = "Bittensor subnet ID (27 for finney production, 15 for test/development)"
  type        = number
  default     = 27
}

variable "bittensor_sandbox_netuid" {
  description = "Bittensor sandbox subnet ID (15 for development/testing)"
  type        = number
  default     = 15
}

variable "deploy_production" {
  description = "Whether to deploy production environment"
  type        = bool
  default     = true
}

variable "deploy_development" {
  description = "Whether to deploy development environment"
  type        = bool
  default     = true
}

variable "production_bittensor_network" {
  description = "Bittensor network for production (finney)"
  type        = string
  default     = "finney"
}

variable "production_bittensor_netuid" {
  description = "Bittensor subnet ID for production (27)"
  type        = number
  default     = 27
}

variable "development_bittensor_network" {
  description = "Bittensor network for development (test)"
  type        = string
  default     = "test"
}

variable "development_bittensor_netuid" {
  description = "Bittensor subnet ID for development (15)"
  type        = number
  default     = 15
}

variable "vpc_connector_cidr" {
  description = "CIDR range for VPC connector subnet"
  type        = string
  default     = "********/28"
}
