output "production_function_url" {
  value       = var.deploy_production ? google_cloudfunctions2_function.auth_function_production[0].service_config[0].uri : null
  description = "The URL of the production Cloud Function"
}

output "development_function_url" {
  value       = var.deploy_development ? google_cloudfunctions2_function.auth_function_development[0].service_config[0].uri : null
  description = "The URL of the development Cloud Function"
}

output "database_connection" {
  value       = google_sql_database_instance.postgres.connection_name
  description = "The connection name of the Cloud SQL instance"
}

output "pubsub_topics" {
  value = {
    allocation_events  = google_pubsub_topic.allocation_events.name
    miner_events      = google_pubsub_topic.miner_events.name
    system_events     = google_pubsub_topic.system_events.name
    validation_events = google_pubsub_topic.validation_events.name
  }
  description = "The names of all Pub/Sub topics"
}

output "pubsub_topic_paths" {
  value = {
    allocation_events  = "projects/${var.project_id}/topics/${google_pubsub_topic.allocation_events.name}"
    miner_events      = "projects/${var.project_id}/topics/${google_pubsub_topic.miner_events.name}"
    system_events     = "projects/${var.project_id}/topics/${google_pubsub_topic.system_events.name}"
    validation_events = "projects/${var.project_id}/topics/${google_pubsub_topic.validation_events.name}"
  }
  description = "The full paths of all Pub/Sub topics"
}

output "function_service_account" {
  value       = google_service_account.function_service_account.email
  description = "The service account email for the Cloud Function"
}

output "scheduler_service_account" {
  value       = google_service_account.scheduler_service_account.email
  description = "The service account email for the Cloud Scheduler"
}

output "scheduler_job_name" {
  description = "Name of the Cloud Scheduler job for cleanup"
  value       = var.deploy_production ? google_cloud_scheduler_job.cleanup[0].name : "Not deployed"
}

output "database_connection_string" {
  description = "Connection string for the Cloud SQL database"
  value       = "postgresql://${google_sql_user.user.name}:${random_password.db_password.result}@${google_sql_database_instance.postgres.private_ip_address}/${google_sql_database.database.name}"
  sensitive   = true
}


output "cleanup_endpoints" {
  value = {
    production  = var.deploy_production ? "${google_cloudfunctions2_function.auth_function_production[0].service_config[0].uri}/cleanup" : null
    development = var.deploy_development ? "${google_cloudfunctions2_function.auth_function_development[0].service_config[0].uri}/cleanup" : null
  }
  description = "The cleanup endpoint URLs for manual testing (both clean all environments)"
}

output "vpc_network" {
  value       = google_compute_network.vpc.name
  description = "The name of the VPC network"
}

output "vpc_connector" {
  value       = google_vpc_access_connector.connector.name
  description = "The name of the VPC connector"
}

output "database_private_ip" {
  value       = google_sql_database_instance.postgres.private_ip_address
  description = "The private IP address of the Cloud SQL instance"
}

output "private_services_range" {
  value       = google_compute_global_address.private_services_range.address
  description = "The private services IP range"
}