# GCP Project Configuration
project_id = "ni-sn27-frontend-prod"

# Deployment Configuration
region       = "us-central1"  # Update if you prefer a different region
service_name = "validator-token-gateway"

# Environment Deployment Flags
deploy_production  = true   # Deploy production Cloud Function
deploy_development = false   # Deploy development Cloud Function

# Production Bittensor Configuration
production_bittensor_network = "finney"  # Production: finney
production_bittensor_netuid  = 27        # Production: 27

# Development Bittensor Configuration
development_bittensor_network = "test"   # Development: test
development_bittensor_netuid  = 15       # Development: 15

# Shared Configuration
bittensor_sandbox_netuid = 15  # Sandbox/Development netuid (always 15)
