# GCP Project Configuration
project_id = "ni-sn27-frontend-dev"

# Deployment Configuration
region       = "us-central1"  # Update if you prefer a different region
service_name = "validator-token-gateway"

# Environment Deployment Flags
deploy_production  = false   # Deploy production Cloud Function
deploy_development = true   # Deploy development Cloud Function

# Production Bittensor Configuration
production_bittensor_network = "finney"  # Production: finney
production_bittensor_netuid  = 27        # Production: 27

# Development Bittensor Configuration (temporarily using finney due to test network issues)
development_bittensor_network = "finney"   # Temporarily: finney (test network has HTTP 503 issues)
development_bittensor_netuid  = 27         # Temporarily: 27 (matching finney network)

# Shared Configuration
bittensor_sandbox_netuid = 15  # Sandbox/Development netuid (always 15)
