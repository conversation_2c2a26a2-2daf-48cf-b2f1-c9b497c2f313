terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.0"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

# Enable required APIs
resource "google_project_service" "compute_api" {
  service = "compute.googleapis.com"
}

resource "google_project_service" "servicenetworking_api" {
  service = "servicenetworking.googleapis.com"
}

resource "google_project_service" "vpcaccess_api" {
  service = "vpcaccess.googleapis.com"
}

# VPC Network for private connectivity
resource "google_compute_network" "vpc" {
  name                    = "${var.service_name}-vpc"
  auto_create_subnetworks = false

  depends_on = [google_project_service.compute_api]
}

# Subnetwork for general use (e.g., jump hosts)
resource "google_compute_subnetwork" "default" {
  name          = "${var.service_name}-subnet"
  ip_cidr_range = "*********/24" # A standard CIDR range for the subnet
  region        = var.region
  network       = google_compute_network.vpc.id
}

# Private services access for Google services
resource "google_compute_global_address" "private_services_range" {
  name          = "${var.service_name}-private-services-range"
  purpose       = "VPC_PEERING"
  address_type  = "INTERNAL"
  prefix_length = 16
  network       = google_compute_network.vpc.id
}

# Private connection for Google services
resource "google_service_networking_connection" "private_vpc_connection" {
  network                 = google_compute_network.vpc.id
  service                 = "servicenetworking.googleapis.com"
  reserved_peering_ranges = [google_compute_global_address.private_services_range.name]

  depends_on = [google_project_service.servicenetworking_api]
}

# VPC Connector for Cloud Functions
resource "google_vpc_access_connector" "connector" {
  name          = "vtg-vpc-connector"
  region        = var.region
  ip_cidr_range = var.vpc_connector_cidr
  network       = google_compute_network.vpc.name

  min_instances = 2
  max_instances = 3

  depends_on = [google_project_service.vpcaccess_api]
}

# Cloud SQL PostgreSQL instance with private IP
resource "google_sql_database_instance" "postgres" {
  name             = "${var.service_name}-db"
  database_version = "POSTGRES_16"
  region           = var.region

  settings {
    tier = "db-f1-micro"

    backup_configuration {
      enabled = true
    }

    ip_configuration {
      ipv4_enabled                                  = false
      private_network                               = google_compute_network.vpc.id
      enable_private_path_for_google_cloud_services = true
    }
  }

  depends_on = [google_service_networking_connection.private_vpc_connection]
}

# Create database
resource "google_sql_database" "database" {
  name     = "validator_gateway"
  instance = google_sql_database_instance.postgres.name
}

# Create database user
resource "google_sql_user" "user" {
  name     = "gateway_user"
  instance = google_sql_database_instance.postgres.name
  password = random_password.db_password.result
}

# Redis instance removed - using Cloud SQL for all storage

# Pub/Sub topics
resource "google_pubsub_topic" "allocation_events" {
  name = "allocation-events"
  # Set message retention to 7 days (in seconds)
  message_retention_duration = "604800s" 
}

resource "google_pubsub_topic" "miner_events" {
  name = "miner-events"
  # Set message retention to 7 days (in seconds)
  message_retention_duration = "604800s" 
}

resource "google_pubsub_topic" "system_events" {
  name = "system-events"
  # Set message retention to 7 days (in seconds)
  message_retention_duration = "604800s" 
}

resource "google_pubsub_topic" "validation_events" {
  name = "validation-events"
  # Set message retention to 7 days (in seconds)
  message_retention_duration = "604800s" 
}

# Secret for JWT
resource "google_secret_manager_secret" "jwt_secret" {
  secret_id = "${var.service_name}-jwt-secret"

  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}

resource "google_secret_manager_secret_version" "jwt_secret_version" {
  secret      = google_secret_manager_secret.jwt_secret.id
  secret_data = random_password.jwt_secret.result
}

# Generate random password for JWT secret
resource "random_password" "jwt_secret" {
  length  = 32
  special = true
}

# Generate random password for database
resource "random_password" "db_password" {
  length  = 16
  special = true
}

# Secret for database password
resource "google_secret_manager_secret" "db_password" {
  secret_id = "${var.service_name}-db-password"

  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}

resource "google_secret_manager_secret_version" "db_password_version" {
  secret      = google_secret_manager_secret.db_password.id
  secret_data = random_password.db_password.result
}

# Redis password secret removed - no longer using Redis

# Create service account for Cloud Function
resource "google_service_account" "function_service_account" {
  account_id   = "validator-gateway-func-sa"
  display_name = "Service Account for ${var.service_name} Cloud Function"
}

# Grant Secret Manager access to Cloud Function service account
resource "google_project_iam_member" "secret_accessor" {
  project = var.project_id
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${google_service_account.function_service_account.email}"
}

# Grant Cloud SQL Client access to Cloud Function service account
resource "google_project_iam_member" "sql_client" {
  project = var.project_id
  role    = "roles/cloudsql.client"
  member  = "serviceAccount:${google_service_account.function_service_account.email}"
}

# Grant Pub/Sub Publisher access to Cloud Function service account
resource "google_project_iam_member" "pubsub_publisher" {
  project = var.project_id
  role    = "roles/pubsub.publisher"
  member  = "serviceAccount:${google_service_account.function_service_account.email}"
}

# Grant Pub/Sub Subscriber role to Cloud Function service account
resource "google_project_iam_member" "pubsub_subscriber" {
  project = var.project_id
  role    = "roles/pubsub.subscriber"
  member  = "serviceAccount:${google_service_account.function_service_account.email}"
}

# Grant Pub/Sub Admin role to Cloud Function service account for subscription management
resource "google_project_iam_member" "pubsub_admin" {
  project = var.project_id
  role    = "roles/pubsub.admin"
  member  = "serviceAccount:${google_service_account.function_service_account.email}"
}

# Grant VPC Access User role to Cloud Function service account
resource "google_project_iam_member" "vpc_access_user" {
  project = var.project_id
  role    = "roles/vpcaccess.user"
  member  = "serviceAccount:${google_service_account.function_service_account.email}"
}

# Grant Service Account Token Creator role to Cloud Function service account for self-impersonation
resource "google_service_account_iam_member" "token_creator" {
  service_account_id = google_service_account.function_service_account.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:${google_service_account.function_service_account.email}"
}

# Cloud Storage bucket for function source code
resource "google_storage_bucket" "function_source" {
  name     = "${var.project_id}-${var.service_name}-function-source"
  location = var.region

  uniform_bucket_level_access = true
}

# Create zip file of function source code including core module
# The source directory /tmp/function-package is prepared by the
# scripts/prepare_function_source.sh script before Terraform is run.
data "archive_file" "function_source" {
  type        = "zip"
  source_dir  = "/tmp/function-package"
  output_path = "${path.root}/function-source.zip"
  excludes = [
    "__pycache__",
    "*.pyc",
    ".git",
    "test_function.py",
    "README.md"
  ]
}

# Upload function source to Cloud Storage
resource "google_storage_bucket_object" "function_source" {
  name   = "function-source-${data.archive_file.function_source.output_md5}.zip"
  bucket = google_storage_bucket.function_source.name
  source = data.archive_file.function_source.output_path
}

# Production Cloud Function (Gen 2)
resource "google_cloudfunctions2_function" "auth_function_production" {
  count       = var.deploy_production ? 1 : 0
  name        = "${var.service_name}-auth-production"
  location    = var.region
  description = "Production authentication function for validator token gateway"

  build_config {
    runtime     = "python312"
    entry_point = "auth_handler"
    source {
      storage_source {
        bucket = google_storage_bucket.function_source.name
        object = google_storage_bucket_object.function_source.name
      }
    }
  }

  service_config {
    max_instance_count    = 20
    available_memory      = "2048M"
    available_cpu         = "1"
    timeout_seconds       = 60
    service_account_email = google_service_account.function_service_account.email

    vpc_connector                 = google_vpc_access_connector.connector.id
    vpc_connector_egress_settings = "PRIVATE_RANGES_ONLY"

    environment_variables = {
      DATABASE_URL                     = "postgresql://${google_sql_user.user.name}:${random_password.db_password.result}@${google_sql_database_instance.postgres.private_ip_address}/${google_sql_database.database.name}"
      JWT_SECRET                       = google_secret_manager_secret.jwt_secret.secret_id
      DB_PASSWORD                      = google_secret_manager_secret.db_password.secret_id
      BITTENSOR_NETWORK                = var.production_bittensor_network
      BITTENSOR_NETUID                 = var.production_bittensor_netuid
      BITTENSOR_SANDBOX_NETUID         = var.bittensor_sandbox_netuid
      SERVICE_NAME                     = var.service_name
      GCP_PROJECT_ID                   = var.project_id
      ENVIRONMENT                      = "production"
      GCP_PUBSUB_ALLOCATION_EVENTS     = google_pubsub_topic.allocation_events.name
      GCP_PUBSUB_MINER_EVENTS          = google_pubsub_topic.miner_events.name
      GCP_PUBSUB_SYSTEM_EVENTS         = google_pubsub_topic.system_events.name
      GCP_PUBSUB_VALIDATION_EVENTS     = google_pubsub_topic.validation_events.name
      # GCP_PUBSUB_SERVICE_ACCOUNT = google_service_account.function_service_account.email  # Removed to use Application Default Credentials
    }
  }

  depends_on = [
    google_storage_bucket_object.function_source,
    google_sql_database_instance.postgres,
    google_secret_manager_secret_version.jwt_secret_version,
    google_secret_manager_secret_version.db_password_version,
    google_vpc_access_connector.connector
  ]
}

# Development Cloud Function (Gen 2)
resource "google_cloudfunctions2_function" "auth_function_development" {
  count       = var.deploy_development ? 1 : 0
  name        = "${var.service_name}-auth-development"
  location    = var.region
  description = "Development authentication function for validator token gateway"

  build_config {
    runtime     = "python312"
    entry_point = "auth_handler"
    source {
      storage_source {
        bucket = google_storage_bucket.function_source.name
        object = google_storage_bucket_object.function_source.name
      }
    }
  }

  service_config {
    max_instance_count    = 20
    available_memory      = "2048M"
    available_cpu         = "1"
    timeout_seconds       = 60
    service_account_email = google_service_account.function_service_account.email

    vpc_connector                 = google_vpc_access_connector.connector.id
    vpc_connector_egress_settings = "PRIVATE_RANGES_ONLY"

    environment_variables = {
      DATABASE_URL                     = "postgresql://${google_sql_user.user.name}:${random_password.db_password.result}@${google_sql_database_instance.postgres.private_ip_address}/${google_sql_database.database.name}"
      JWT_SECRET                       = google_secret_manager_secret.jwt_secret.secret_id
      DB_PASSWORD                      = google_secret_manager_secret.db_password.secret_id
      BITTENSOR_NETWORK                = var.development_bittensor_network
      BITTENSOR_NETUID                 = var.development_bittensor_netuid
      BITTENSOR_SANDBOX_NETUID         = var.bittensor_sandbox_netuid
      SERVICE_NAME                     = var.service_name
      GCP_PROJECT_ID                   = var.project_id
      ENVIRONMENT                      = "development"
      GCP_PUBSUB_ALLOCATION_EVENTS     = google_pubsub_topic.allocation_events.name
      GCP_PUBSUB_MINER_EVENTS          = google_pubsub_topic.miner_events.name
      GCP_PUBSUB_SYSTEM_EVENTS         = google_pubsub_topic.system_events.name
      GCP_PUBSUB_VALIDATION_EVENTS     = google_pubsub_topic.validation_events.name
      # GCP_PUBSUB_SERVICE_ACCOUNT = google_service_account.function_service_account.email  # Removed to use Application Default Credentials
    }
  }

  depends_on = [
    google_storage_bucket_object.function_source,
    google_sql_database_instance.postgres,
    google_secret_manager_secret_version.jwt_secret_version,
    google_secret_manager_secret_version.db_password_version,
    google_vpc_access_connector.connector
  ]
}

# IAM policy for Production Cloud Function public access
resource "google_cloudfunctions2_function_iam_member" "public_access_production" {
  count          = var.deploy_production ? 1 : 0
  project        = var.project_id
  location       = var.region
  cloud_function = google_cloudfunctions2_function.auth_function_production[0].name
  role           = "roles/cloudfunctions.invoker"
  member         = "allUsers"
}

# IAM policy for Production Cloud Run service public access
resource "google_cloud_run_service_iam_member" "public_access_production" {
  count    = var.deploy_production ? 1 : 0
  project  = var.project_id
  location = var.region
  service  = google_cloudfunctions2_function.auth_function_production[0].name
  role     = "roles/run.invoker"
  member   = "allUsers"
}

# IAM policy for Development Cloud Function public access
resource "google_cloudfunctions2_function_iam_member" "public_access_development" {
  count          = var.deploy_development ? 1 : 0
  project        = var.project_id
  location       = var.region
  cloud_function = google_cloudfunctions2_function.auth_function_development[0].name
  role           = "roles/cloudfunctions.invoker"
  member         = "allUsers"
}

# IAM policy for Development Cloud Run service public access
resource "google_cloud_run_service_iam_member" "public_access_development" {
  count    = var.deploy_development ? 1 : 0
  project  = var.project_id
  location = var.region
  service  = google_cloudfunctions2_function.auth_function_development[0].name
  role     = "roles/run.invoker"
  member   = "allUsers"
}

# Service account for Cloud Scheduler
resource "google_service_account" "scheduler_service_account" {
  account_id   = "validator-gateway-scheduler-sa"
  display_name = "Service Account for ${var.service_name} Cloud Scheduler"
}

# Grant Cloud Functions Invoker role to scheduler service account for production
resource "google_cloudfunctions2_function_iam_member" "scheduler_invoker_production" {
  count          = var.deploy_production ? 1 : 0
  project        = var.project_id
  location       = var.region
  cloud_function = google_cloudfunctions2_function.auth_function_production[0].name
  role           = "roles/cloudfunctions.invoker"
  member         = "serviceAccount:${google_service_account.scheduler_service_account.email}"
}

# Cloud Scheduler job for daily cleanup (runs only in production, cleans all environments)
resource "google_cloud_scheduler_job" "cleanup" {
  count       = var.deploy_production ? 1 : 0
  name        = "${var.service_name}-cleanup"
  description = "Daily cleanup of expired tokens across all environments"
  schedule    = "0 2 * * *"  # Daily at 2 AM UTC
  time_zone   = "UTC"
  region      = var.region

  http_target {
    http_method = "GET"
    uri         = "${google_cloudfunctions2_function.auth_function_production[0].service_config[0].uri}/cleanup"

    oidc_token {
      service_account_email = google_service_account.scheduler_service_account.email
      audience              = google_cloudfunctions2_function.auth_function_production[0].service_config[0].uri
    }
  }

  depends_on = [
    google_cloudfunctions2_function.auth_function_production,
    google_service_account.scheduler_service_account
  ]
}
