# Changelog

All notable changes to the Validator Token Gateway project will be documented in this file.

## [2.0.0] - 2025-05-29 - Cloud Function Architecture

### 🚀 Major Architecture Change
- **Primary Architecture**: Cloud Functions Gen2 (serverless)
- **Secondary Architecture**: FastAPI (local development only)
- **Infrastructure**: Terraform-managed Cloud SQL + Secret Manager + Pub/Sub
- **Deployment**: One-command deployment with `make deploy-function`

### ✨ Key Features
- **Serverless Authentication**: JWT and OAuth2 Pub/Sub tokens
- **Bittensor Integration**: Cryptographic signature verification
- **Auto-scaling**: Pay-per-request with 0-1000 instance scaling
- **Secure Storage**: Cloud SQL PostgreSQL with Secret Manager
- **Local Development**: Complete Docker + GCP emulator stack

### 📦 Package Management
- **Migrated to uv**: 10-100x faster than Poetry
- **Standard Python packaging**: Using `pyproject.toml` with `[project]` section
- **Reliable dependencies**: `uv.lock` for consistent builds

### 🎯 Deployment & Testing
- **One-command deployment**: `make deploy-function`
- **Local GCP emulators**: Complete local testing environment
- **Validator demo**: Real-time Pub/Sub communication demo
- **89% test coverage**: Comprehensive test suite

### 🔧 Developer Experience
- **50+ Make commands**: Complete automation
- **Local development**: Docker + FastAPI stack
- **GCP testing**: Emulators for offline development
- **Documentation**: Comprehensive guides and references

---

## Previous Versions

### [1.0.0] - FastAPI Microservice
- FastAPI application with Redis
- Basic validator authentication
- JWT token management
- Docker containerization
- Initial test suite
