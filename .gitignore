# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/

# Poetry
.venv/

# Terraform
.terraform/
*.tfstate
*.tfstate.backup
.terraform.lock.hcl
terraform/function-source.zip

# GCP
credentials.json

# IDE
.idea/
.vscode/

# Test files
tests/output/
tests/*.log
.coverage
.coverage.*
coverage.xml
htmlcov/
.pytest_cache/
.tox/

# Database files and backups
*.db
*.sqlite
*.sqlite3
test.db
backup_*.sql

# Requirements exports
requirements.txt
requirements-dev.txt

# Local GCP testing
local-service-account.json
.gcp-emulator-data/

# Local validator simulation
local-validators/*.log
local-validators/validator_*.json.bak
local-validators/*.pid
local-validators/*.tmp

# Environment files
.env
.env.local
.env.*.local
.env.development
.env.production
.env.test

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor files
*.swp
*.swo
*~
.vimrc.local

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Docker
.dockerignore.bak
docker-compose.override.yml

# Local development
.local/
local-config.json
local-settings.json

# Backup files
*.bak
*.backup
*~

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Project-specific temporary files
test_validator_simple.py
validator_test_*.py
demo_output_*.txt
pubsub_monitor_*.log

# GCP Emulator data
.gcp-emulator-data/
gcp-emulator-data/
pubsub-emulator-data/
secret-manager-data/

# Local testing artifacts
local-test-results/
test-output/
demo-logs/
validator-logs/

# Monitoring and dashboard files
monitor-dashboard.html
pubsub-activity.json
validator-stats.json

# Development scripts (temporary) - exclude from root only
/test_*.py
/debug_*.py
/temp_*.py
/scratch_*.py

# Local configuration overrides
local.env
.env.override
config.local.json
settings.local.json

# Performance and profiling
*.prof
*.profile
.profiling/

# Security and secrets (additional patterns)
*.key
*.pem
*.crt
*.p12
*.pfx
service-account-*.json
gcp-credentials-*.json
auth-token-*.json

# Jupyter Notebook checkpoints
.ipynb_checkpoints/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# Python version management
.python-version
.pyenv-version

# Pre-commit
.pre-commit-config.yaml.bak

# Local development overrides
docker-compose.local.yml
docker-compose.dev.yml
docker-compose.override.local.yml

# Documentation builds
docs/_build/
docs/build/
site/

# Misc
*.orig
