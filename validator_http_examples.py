#!/usr/bin/env python3
"""
Validator HTTP Request Examples
===============================

This file shows how validators should make HTTP requests to the Token Gateway API.
Includes authentication, token requests, and Pub/Sub token requests.
"""

import time

import bittensor as bt
import requests


class ValidatorClient:
    """HTTP client for validator interactions with the Token Gateway."""

    def __init__(self, api_base_url: str, wallet: bt.wallet):
        """
        Initialize the validator client.

        Args:
            api_base_url: Base URL of the Token Gateway API (e.g., "http://localhost:8000")
            wallet: Bittensor wallet containing the validator's keypair
        """
        self.api_base_url = api_base_url.rstrip("/")
        self.wallet = wallet
        self.hotkey = wallet.hotkey.ss58_address
        self.jwt_token: str | None = None
        self.jwt_expires_at: float | None = None

    def _get_auth_message(self) -> str:
        """Get the authentication message that needs to be signed."""
        # For development environment (netuid 15)
        netuid = 15  # This should match the API's active_netuid
        return (
            f"Authenticate to Bittensor Subnet {netuid}: validator-token-gateway-auth"
        )

    def _sign_message(self, message: str) -> str:
        """Sign a message with the validator's hotkey."""
        signature = self.wallet.hotkey.sign(message.encode()).hex()
        return signature

    def authenticate(self) -> dict:
        """
        Step 1: Authenticate with Bittensor signature to get JWT token.

        Returns:
            dict: Response containing access_token, expires_at, etc.
        """
        # Create the authentication message
        message = self._get_auth_message()

        # Sign the message
        signature = self._sign_message(message)

        # Create authorization header
        auth_header = f"Bittensor {self.hotkey}:{signature}"

        # Make the request
        url = f"{self.api_base_url}/auth/token"
        headers = {"Authorization": auth_header, "Content-Type": "application/json"}

        print(f"🔐 Authenticating validator: {self.hotkey}")
        print(f"📝 Message signed: {message}")
        print(f"🔗 Request URL: {url}")

        response = requests.post(url, headers=headers)

        if response.status_code == 200:
            data = response.json()
            self.jwt_token = data["access_token"]
            self.jwt_expires_at = data["expires_at"]
            print("✅ Authentication successful!")
            print(f"🎫 JWT Token: {self.jwt_token[:50]}...")
            print(f"⏰ Expires at: {time.ctime(self.jwt_expires_at)}")
            return data
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            response.raise_for_status()

    def request_pubsub_token(self) -> dict:
        """
        Step 2: Request Pub/Sub token using JWT authentication.

        Returns:
            dict: Response containing Pub/Sub access_token, topic_path, etc.
        """
        if not self.jwt_token:
            raise ValueError("Must authenticate first to get JWT token")

        if self.jwt_expires_at and time.time() > self.jwt_expires_at:
            print("🔄 JWT token expired, re-authenticating...")
            self.authenticate()

        # Create authorization header with JWT token
        auth_header = f"Bearer {self.jwt_token}"

        # Make the request
        url = f"{self.api_base_url}/auth/pubsub-token"
        headers = {"Authorization": auth_header, "Content-Type": "application/json"}

        print("📨 Requesting Pub/Sub token...")
        print(f"🔗 Request URL: {url}")

        response = requests.post(url, headers=headers)

        if response.status_code == 200:
            data = response.json()
            print("✅ Pub/Sub token received!")
            print(f"🎫 Pub/Sub Token: {data['access_token'][:50]}...")
            print("📋 Available Topic Paths:")
            for topic_type, path in data['topic_paths'].items():
                print(f"   {topic_type}: {path}")
            print(f"⏰ Expires in: {data['expires_in']} seconds")
            return data
        else:
            print(f"❌ Pub/Sub token request failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            response.raise_for_status()

    def get_validator_info(self) -> dict:
        """
        Get validator information using JWT token.

        Returns:
            dict: Validator information (hotkey, uid, stake, trust)
        """
        if not self.jwt_token:
            raise ValueError("Must authenticate first to get JWT token")

        # Create authorization header with JWT token
        auth_header = f"Bearer {self.jwt_token}"

        # Make the request
        url = f"{self.api_base_url}/validators/me/token"
        headers = {"Authorization": auth_header, "Content-Type": "application/json"}

        print("👤 Getting validator info...")
        print(f"🔗 Request URL: {url}")

        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            data = response.json()
            print("✅ Validator info received!")
            print(f"🔑 Hotkey: {data['hotkey']}")
            print(f"🆔 UID: {data['uid']}")
            print(f"💰 Stake: {data['stake']}")
            print(f"🤝 Trust: {data['trust']}")
            return data
        else:
            print(f"❌ Validator info request failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            response.raise_for_status()

    def full_flow_example(self) -> tuple[dict, dict, dict]:
        """
        Complete example: Authenticate -> Get Pub/Sub token -> Get validator info.

        Returns:
            tuple: (auth_response, pubsub_response, validator_info)
        """
        print("🚀 Starting complete validator authentication flow...")
        print("=" * 60)

        # Step 1: Authenticate
        auth_response = self.authenticate()
        print()

        # Step 2: Request Pub/Sub token
        pubsub_response = self.request_pubsub_token()
        print()

        # Step 3: Get validator info
        validator_info = self.get_validator_info()
        print()

        print("🎉 Complete flow successful!")
        return auth_response, pubsub_response, validator_info


def main():
    """Example usage of the ValidatorClient."""
    # Configuration
    API_BASE_URL = "http://localhost:8000"
    WALLET_NAME = "validator"  # Your wallet name
    HOTKEY_NAME = "default"  # Your hotkey name

    print("🎭 Validator HTTP Client Example")
    print("=" * 40)

    try:
        # Load wallet (you need to have a wallet set up)
        print(f"📂 Loading wallet: {WALLET_NAME}/{HOTKEY_NAME}")
        wallet = bt.wallet(name=WALLET_NAME, hotkey=HOTKEY_NAME)

        # Create client
        client = ValidatorClient(API_BASE_URL, wallet)

        # Run complete flow
        auth_resp, pubsub_resp, validator_info = client.full_flow_example()

        print("\n📊 Summary:")
        print("  • Authenticated: ✅")
        print(f"  • JWT Token: {auth_resp['access_token'][:20]}...")
        print(f"  • Pub/Sub Token: {pubsub_resp['access_token'][:20]}...")
        print(f"  • Validator UID: {validator_info['uid']}")
        print(f"  • Validator Stake: {validator_info['stake']}")

    except Exception as e:
        print(f"❌ Error: {e}")
        print("\n💡 Make sure:")
        print("  1. The API is running at http://localhost:8000")
        print("  2. You have a Bittensor wallet set up")
        print("  3. Your validator is registered on the subnet")


if __name__ == "__main__":
    main()
