# Quick Reference - Validator Token Gateway

## 🚀 Getting Started (First Time)
```bash
make setup-dev    # Complete development setup
make status       # Check environment
make dev          # Start development server
```

## 🔧 Daily Development
```bash
make dev          # Start development server
make test-cov     # Run tests with coverage
make quick-check  # Lint + format + test
make docker-up    # Start full stack (API + DB + GCP emulators)
```

## 🧪 Testing
```bash
make test         # All tests
make test-cov     # Tests + coverage report
make test-watch   # Tests in watch mode
make test-unit    # Unit tests only
make test-routes  # Route tests only
```

## 🎨 Code Quality
```bash
make lint         # Check code quality
make lint-fix     # Auto-fix issues
make format       # Format code
make pre-commit   # Run all pre-commit hooks
```

## 🐳 Docker
```bash
make docker-up    # Start all services
make docker-down  # Stop all services
make docker-logs  # View logs
make docker-clean # Clean up resources
```

## 🗄️ Database
```bash
make init-db      # Initialize tables
make db-shell     # Open database shell
make backup-db    # Create backup
```

## 🎭 Validator Demo (NEW!)
```bash
make local-demo   # 🎯 RECOMMENDED: Complete local setup
make run-validator1  # Start Validator Alpha
make run-validator2  # Start Validator Beta
make monitor-pubsub  # Real-time communication dashboard
make demo-short      # Quick 30-second demo
```

## ☁️ Local GCP Testing
```bash
make setup-local-gcp    # Setup GCP emulators (like LocalStack)
make gcp-status         # Check emulator status
make test-pubsub-emulator  # Test Pub/Sub connectivity
make clean-gcp          # Clean up GCP resources
```

## 🆘 Troubleshooting
```bash
make help         # Show all commands
make status       # Check environment status
make info         # Project information
make clean        # Clean cache files
```

## 📊 Current Status
- **Coverage**: 89% (Target: 80%) ✅
- **Tests**: 74 passing, 5 skipped ✅
- **Structure**: Organized by app modules ✅

## 🔗 Quick Links
- **API**: http://localhost:8000
- **Docs**: http://localhost:8000/docs
- **Coverage Report**: `htmlcov/index.html`
- **Full Guide**: `DEVELOPMENT.md`
